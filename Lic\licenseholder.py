import base64
import datetime
import os.path
from dataclasses import dataclass

import xmltodict
from django.conf import settings
from gmssl import sm2, sm4


@dataclass
class License:
    def __init__(self, **kwargs):
        self.Issuer = kwargs.get('Issuer')
        self.Signature = kwargs.get('Signature')
        issued_at = kwargs.get('IssuedAt')
        expiration = kwargs.get('Expiration')
        if issued_at:
            self.IssuedAt = datetime.datetime.strptime(issued_at, "%Y-%m-%d %H:%M:%S")
        if expiration:
            self.Expiration = datetime.datetime.strptime(expiration, "%Y-%m-%d %H:%M:%S")

    Issuer: str
    IssuedAt: datetime.datetime
    Expiration: datetime.datetime
    Signature: str


class LicenseHolder:
    def __init__(self):
        self.public_key = "041E9EBEAAE518C6D85A3001CB5ADB2BB3C2003D4760F734DA75500D1ACA4AB8F37FA58E74D5B5966A66EBE4C4F0C0D2C645FA9417F3E4583F1E081378036C3927"
        self.parser_key = bytes(self.public_key[16:32], encoding="utf-8")
        self.times = 1

        self._license = None
        self._license_path = None
        self._license_uptime = None

    @property
    def application_name(self):
        return settings.APPLICATION_NAME.lower().replace(" ", "_")

    @property
    def license_file_name(self):
        application_to_file_name = self.application_name.replace("_", ".")
        license_file_name = f"license.{application_to_file_name}.lic"
        return license_file_name

    @property
    def license_path(self):
        if self._license_path is not None:
            return self._license_path

        license_file_name = self.license_file_name
        license_path = f"/etc/ztbory/{license_file_name}"
        base_dir = os.path.abspath(os.path.dirname(__file__))
        if os.path.exists(os.path.join(base_dir, "./../ztbory/", license_file_name)):
            license_path = os.path.join(base_dir, "./../ztbory/", license_file_name)
        if os.path.exists(os.path.join(base_dir, "./../../ztbory/", license_file_name)):
            license_path = os.path.join(base_dir, "./../../ztbory/", license_file_name)
        self._license_path = os.path.abspath(license_path)
        return self._license_path

    @property
    def license(self):
        if self._license is not None and self._license_uptime == os.path.getmtime(self._license_path):
            return self._license

        self._license = None
        try:
            with open(self.license_path, "rb") as file:
                parser = sm4.CryptSM4()
                parser.set_key(self.parser_key, sm4.SM4_DECRYPT)
                plain_content = parser.crypt_ecb(file.read())
                xml = xmltodict.parse(plain_content.decode("utf-8"))

                lc = License(**xml.get("License"))

                sign_content = f"ISSUER={lc.Issuer}; ISSUED_AT={int(lc.IssuedAt.timestamp())}; EXPIRATION={int(lc.Expiration.timestamp())};"
                cryptor = sm2.CryptSM2(None, self.public_key)
                if all([
                    cryptor.verify(base64.b64decode(lc.Signature).hex(), sign_content.encode("utf-8")),
                    lc.Issuer == f"ztbory_{self.application_name}"
                ]):
                    self._license = lc
                    self._license_uptime = os.path.getmtime(self.license_path)
        except Exception as e:
            print(e)

        return self._license

    def verify(self):
        return True, None
        # if self.license is None:
        #     return False, Exception("证书无效")
        # if self.license.IssuedAt > datetime.datetime.now() or self.license.Expiration < datetime.datetime.now():
        #     return False, Exception("证书已过期")
        # return True, None


lic = LicenseHolder()
