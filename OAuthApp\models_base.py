#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from django.db import models


class CityRegion(models.Model):
    region_id = models.Char<PERSON>ield(unique=True, max_length=45)
    name = models.CharField(max_length=45, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    coding = models.CharField(max_length=45, blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    parent_id = models.CharField(max_length=45, blank=True, null=True)
    grade = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=250, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'city_region'
        app_label = 'ljfl_db'


class FactoryLocation(models.Model):
    area = models.CharField(max_length=100, blank=True, null=True)
    quancheng = models.IntegerField(blank=True, null=True)
    name = models.CharField(max_length=100, blank=True, null=True)
    standing_name = models.CharField(max_length=100, blank=True, null=True)
    alias_name = models.CharField(max_length=100, blank=True, null=True)
    address = models.CharField(max_length=100, blank=True, null=True)
    mark = models.CharField(max_length=100, blank=True, null=True)
    type = models.CharField(max_length=100, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    factory = models.CharField(max_length=100, blank=True, null=True)
    process_type = models.CharField(max_length=200, blank=True, null=True)
    model_name = models.CharField(max_length=64, blank=True, null=True)
    operation_condition = models.CharField(max_length=12, blank=True, null=True)
    print_type = models.CharField(max_length=200, blank=True, null=True)
    manage_unit = models.CharField(max_length=255, blank=True, null=True)
    operate_unit = models.CharField(max_length=255, blank=True, null=True)
    contacts = models.CharField(max_length=45, blank=True, null=True)
    phone = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    factory_location_id = models.CharField(max_length=45)
    factory_type = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    is_actual_operation = models.BooleanField("是否运行中")
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    is_camera = models.IntegerField(default=0, blank=True, null=True)
    enter_monitor = models.IntegerField(default=0, blank=True, null=True)
    weighbridge_monitor = models.IntegerField(default=0, blank=True, null=True)
    discharge_monitor = models.IntegerField(default=0, blank=True, null=True)
    delivery_monitor = models.IntegerField(default=0, blank=True, null=True)
    pic = models.CharField(max_length=1000, blank=True, null=True)
    is_weight = models.IntegerField(default=0, blank=True, null=True)
    city_system = models.IntegerField(default=0, blank=True, null=True)
    door_camera_num = models.IntegerField(default=0, blank=True, null=True)
    plate_camera_num = models.IntegerField(default=0, blank=True, null=True)
    weighbridge_camera_num = models.IntegerField(default=0, blank=True, null=True)
    discharging_camera_num = models.IntegerField(default=0, blank=True, null=True)
    trans_camera_num = models.IntegerField(default=0, blank=True, null=True)
    price = models.IntegerField(default=0, blank=True, null=True)
    subsidies = models.IntegerField(default=0, blank=True, null=True)
    hgzx_name = models.CharField(max_length=100, blank=True, null=True)
    hgzx_sort = models.IntegerField(default=0, blank=True, null=True)
    hgzx_area = models.IntegerField(default=0, blank=True, null=True)
    hgzx_sstype = models.IntegerField(default=0, blank=True, null=True)
    hgzx_factory_type = models.CharField(max_length=255, blank=True, null=True)
    clean_code = models.CharField(max_length=45, blank=True, null=True)
    clean_no = models.CharField(max_length=45, blank=True, null=True)
    bill_no_prefix = models.CharField(max_length=10, blank=True, null=True)
    build_date = models.DateField(default=None, verbose_name="建厂时间")
    opertaion_date = models.DateField(default=None, verbose_name="运营时间")
    stop_date = models.DateField(default=None, verbose_name="停产时间")

    class Meta:
        managed = False
        db_table = "factory_location"
        app_label = "ljfl_db"
