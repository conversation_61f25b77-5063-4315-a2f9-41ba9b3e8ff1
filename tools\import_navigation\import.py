#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import os

import django
import xlrd

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
django.setup()

from OAuthApp.models import Navigation


def import_navigation():
    excel = xlrd.open_workbook('./tools/import_navigation/navigation.xlsx')
    sheet = excel.sheet_by_name('Sheet1')
    last_level = -1
    mapper = []
    for i, row in enumerate(sheet.get_rows()):
        if i == 0:
            continue
        row = [
            itm.value for itm in row
        ]
        print(row)
        level, navType, name, componentName, componentPath, icon, path, activeNavigation, permission, isVisible = row
        isVisible = 0 if isVisible == 0 else 1

        mapper = mapper[:int(level - 1)]

        navigation = Navigation(
            site_id=3,
            parent_id=mapper[-1].id if mapper else 0,
            type=navType,
            name=name,
            permission=permission,
            component_name=componentName,
            component_path=componentPath,
            icon=icon,
            path=path,
            is_visible=isVisible,
            active_navigation=activeNavigation
        )
        navigation.save()

        last_level = level
        mapper.append(navigation)


if __name__ == '__main__':
    import_navigation()
