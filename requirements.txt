annotated-types==0.6.0 ; python_version >= "3.9" and python_version < "4.0"
apscheduler==3.10.1 ; python_version >= "3.9" and python_version < "4.0"
asgiref==3.7.2 ; python_version >= "3.9" and python_version < "4.0"
async-timeout==4.0.2 ; python_version >= "3.9" and python_full_version <= "3.11.2"
certifi==2023.7.22 ; python_version >= "3.9" and python_version < "4.0"
cffi==1.15.1 ; python_version >= "3.9" and python_version < "4.0"
charset-normalizer==3.2.0 ; python_version >= "3.9" and python_version < "4.0"
cryptography==41.0.2 ; python_version >= "3.9" and python_version < "4.0"
deprecated==1.2.14 ; python_version >= "3.9" and python_version < "4.0"
django-cors-headers==4.2.0 ; python_version >= "3.9" and python_version < "4.0"
django-filter==23.2 ; python_version >= "3.9" and python_version < "4.0"
django-oauth-toolkit==2.3.0 ; python_version >= "3.9" and python_version < "4.0"
django-redis==5.3.0 ; python_version >= "3.9" and python_version < "4.0"
django==4.1.10 ; python_version >= "3.9" and python_version < "4.0"
djangorestframework==3.14.0 ; python_version >= "3.9" and python_version < "4.0"
gevent==23.7.0 ; python_version >= "3.9" and python_version < "4.0"
gmssl==3.2.2 ; python_version >= "3.9" and python_version < "4.0"
greenlet==3.0.0a1 ; platform_python_implementation == "CPython" and python_version < "4.0" and python_version >= "3.9"
gunicorn==21.2.0 ; python_version >= "3.9" and python_version < "4.0"
idna==3.4 ; python_version >= "3.9" and python_version < "4.0"
jwcrypto==1.5.0 ; python_version >= "3.9" and python_version < "4.0"
mysqlclient==2.1.1 ; python_version >= "3.9" and python_version < "4.0"
oauthlib==3.2.2 ; python_version >= "3.9" and python_version < "4.0"
packaging==23.1 ; python_version >= "3.9" and python_version < "4.0"
pycparser==2.21 ; python_version >= "3.9" and python_version < "4.0"
pycryptodome==3.18.0 ; python_version >= "3.9" and python_version < "4.0"
pycryptodomex==3.19.1 ; python_version >= "3.9" and python_version < "4.0"
pydantic-core==2.14.6 ; python_version >= "3.9" and python_version < "4.0"
pydantic==2.5.3 ; python_version >= "3.9" and python_version < "4.0"
pyjwt==2.9.0 ; python_version >= "3.9" and python_version < "4.0"
pytz==2023.3 ; python_version >= "3.9" and python_version < "4.0"
redis==4.6.0 ; python_version >= "3.9" and python_version < "4.0"
requests==2.31.0 ; python_version >= "3.9" and python_version < "4.0"
setuptools==68.0.0 ; python_version >= "3.9" and python_version < "4.0"
six==1.16.0 ; python_version >= "3.9" and python_version < "4.0"
sqlparse==0.4.4 ; python_version >= "3.9" and python_version < "4.0"
typing-extensions==4.7.1 ; python_version >= "3.9" and python_version < "4.0"
tzdata==2023.3 ; python_version >= "3.9" and python_version < "4.0" and (sys_platform == "win32" or platform_system == "Windows")
tzlocal==5.0.1 ; python_version >= "3.9" and python_version < "4.0"
urllib3==2.0.4 ; python_version >= "3.9" and python_version < "4.0"
wrapt==1.15.0 ; python_version >= "3.9" and python_version < "4.0"
xmltodict==0.13.0 ; python_version >= "3.9" and python_version < "4.0"
zope-event==5.0 ; python_version >= "3.9" and python_version < "4.0"
zope-interface==6.0 ; python_version >= "3.9" and python_version < "4.0"
