[tool.poetry]
name = "bj-auth-manager"
version = "0.1.0"
description = ""
authors = ["z<PERSON><PERSON><PERSON> <z<PERSON><PERSON><PERSON>@ztbory.com>"]
readme = "README.md"
packages = [{include = "bj_auth_manager"}]

[tool.poetry.dependencies]
python = "^3.9"
django = "4.1.10"
djangorestframework = "^3.14.0"
django-cors-headers = "^4.2.0"
django-redis = "^5.3.0"
gunicorn = "^21.2.0"
gevent = "^23.7.0"
django-oauth-toolkit = "^2.3.0"
django-filter = "^23.2"
apscheduler = "^3.10.1"
pycryptodome = "^3.18.0"
mysqlclient = "2.1.1"
gmssl = "^3.2.2"
xmltodict = "^0.13.0"
pydantic = "^2.5.3"
pyjwt = "^2.9.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
