#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import re
import time
import uuid
import string
import random
import logging
import datetime

logger = logging.getLogger('django')


def get_uuid():
    return str(uuid.uuid1()).replace('-', '')


def get_current_timestamp():
    return int(time.time())


def get_appid():
    """
    获取AppId
    :return:
    """
    start_time = datetime.datetime(2017, 1, 1)
    end_time = datetime.datetime.now()
    return int(end_time.timestamp() - start_time.timestamp())


def get_random_secret(length=32):
    """
    生成随机密钥
    :param length:
    :return:
    """
    seeds = string.ascii_letters + string.digits
    return ''.join(random.choice(seeds) for _ in range(length))


def get_coding(coding, grade):
    """
    获取指定级别coding
    :param coding:
    :param grade:
    :return:
    """
    coding_grade = 0
    for i in range(3, -1, -1):
        coding_grade += 1
        if coding.endswith('0' * (i * 3)):
            break
    if grade > coding_grade:
        return ''
    return coding[:grade * 3] + '0' * ((4 - grade) * 3)


def fill_coding(city_coding='', area_coding='', street_coding='', comm_coding='', is_strict=False):
    """
    自动填充各个coding
    :param city_coding:
    :param area_coding:
    :param street_coding:
    :param comm_coding:
    :param is_strict: 是否严格模式，如果严格模式会返回coding不匹配，否则由低到高依次匹配出来
    :return:
    """
    is_success = True
    error = ''
    _city_coding, _area_coding, _street_coding, _comm_coding = '', '', '', ''
    _comm_coding = comm_coding
    if _comm_coding:
        _street_coding = get_coding(_comm_coding, 3)
    if _street_coding:
        _area_coding = get_coding(_street_coding, 2)
    if _area_coding:
        _city_coding = get_coding(_area_coding, 1)

    if is_strict and (city_coding, area_coding, street_coding, comm_coding) != (
        _city_coding, _area_coding, _street_coding, _comm_coding):
        is_success = False
        error = '无效的区域组合!'

    if not any([_city_coding, _area_coding, _street_coding, _comm_coding]):
        is_success = False
        error = '无效的区域组合!'

    logger.info((city_coding, area_coding, street_coding, comm_coding))
    logger.info((_city_coding, _area_coding, _street_coding, _comm_coding))
    return dict(is_success=is_success,
                city_coding=_city_coding,
                area_coding=_area_coding,
                street_coding=_street_coding,
                comm_coding=_comm_coding,
                error=error)


def generate_random_number(length=8):
    seeds = string.digits
    return ''.join(random.choice(seeds) for _ in range(length))


def get_ip(request):
    if request.META.get('HTTP_X_FORWARED_FOR'):
        ip = request.META.get("HTTP_X_FORWARED_FOR")
        ip = ip.split(',')[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def random_password(length=8):
    """随机密码"""
    digit = random.choice(string.digits)
    letter_lower = random.choice(string.ascii_lowercase)
    letter_upper = random.choice(string.ascii_uppercase)

    seeds = [digit, letter_lower, letter_upper]
    for i in range(length - 3):
        seeds.append(random.choice(string.digits + string.ascii_letters))
    random.shuffle(seeds)
    return ''.join(seeds)


def validate_password(password):
    """校验密码"""
    pattern = r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d!\"#$%&'()*+,-./:;<=>?@\\\]\[^_`{|}~]{8,32}$"
    return True if re.match(pattern, password) else False
