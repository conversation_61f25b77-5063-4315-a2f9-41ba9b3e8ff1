#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *

DEBUG = False

ENVIRONMENT = 'testing'

# 数智源数据同步接口
SOYUAN_SYNC_HOST = ''
DATAV_SYNC_HOST = 'http://*************:8456'
# 数智源同步任务开启状态
SYNC_SCHEDULER_ON = True

JWT_ENABLED = False
PROXY_ENABLED = False
PROXY_HOST = ''

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3'
    },
    'ljfl_auth_manager_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_auth_manager_db',
        'USER': 'ljfldb',
        'HOST': '***********',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8'},
        "CONN_MAX_AGE": 100,
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'ljfldb',
        'HOST': '***********',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8'},
        "CONN_MAX_AGE": 100,
    }
}

SENTINELS = [
    ("************", 16380),
    ("************", 16380),
    ("***********", 16380),
]

DJANGO_REDIS_CONNECTION_FACTORY = "django_redis.pool.SentinelConnectionFactory"

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "KEY_PREFIX": "api:BjAuth:Test",
        "LOCATION": "redis://ztbrmaster/11",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.SentinelClient",
            "SENTINELS": SENTINELS,
            "REDIS_CLIENT_KWARGS": { 
                "username": "bjauthmanager",
                "password": "1S%Amt1wtWy9Sln%",
            }
        },
    },
}
