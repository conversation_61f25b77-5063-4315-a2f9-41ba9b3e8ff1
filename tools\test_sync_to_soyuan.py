#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import os
import datetime
import django

# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.import")
django.setup()

from Base.utils import get_uuid
from django.contrib.auth.hashers import make_password
from OAuthApp.scheduler.task_sync_to_soyuan import sync_agency_type, sync_manager
from Base.utils.soyuan_syncor import soyuan_syncor, SoyuanManager

manager = SoyuanManager(
    account='gatfsc',
    password='Gat65780226',
    realname='高安屯垃圾焚烧厂',
    level='facility',
    status=0,
    unit='高安屯垃圾焚烧厂',
    facility_type='8d003746392711eb9f70485f99c1b734'
)

result = soyuan_syncor.sync_manager(**manager.to_dict())
print(result)
#
# result = soyuan_syncor.sync_permission(level='area', account=['dongcheng1'], module=['平台首页', '非居民管理', '居民管理'])
# print(result)

# sync_agency_type()
#
# sync_manager()
