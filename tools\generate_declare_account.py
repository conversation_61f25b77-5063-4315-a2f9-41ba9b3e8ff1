#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import os
import datetime
import django
from django.contrib.auth.hashers import make_password
import pypinyin
import openpyxl
from itertools import chain

from Base.utils import get_uuid, generate_random_number

# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.import")
django.setup()

from OAuthApp.models import Manager
from OAuthApp.models_base import CityRegion


def _get_pinyin(words, ignore_char=[]):
    for ch in ignore_char:
        words = words.replace(ch, '')
    char = pypinyin.pinyin(words, style=pypinyin.NORMAL)
    return ''.join(chain(*char))


def _get_pinyin_prefix(words, ignore_char=[]):
    """
    获取首拼
    :param words:
    :param ignore_char:
    :return:
    """
    for ch in ignore_char:
        words = words.replace(ch, '')
    char = pypinyin.pinyin(words, style=pypinyin.NORMAL)
    return ''.join([c[0][0] for c in char])


def generate(area_coding):
    global row_no
    print(area_coding, '\r\n')
    area_coding_prefix = area_coding[:6]
    region = CityRegion.objects.filter(coding__contains=area_coding_prefix,
                                       is_deleted=0,
                                       grade__in=[2, 3]).order_by('grade').all()
    prefix = ''
    suffix = ''
    area = ''
    street = ''
    street_coding = ''
    role = ''
    users = []
    for r in region:
        if r.grade == 2:
            area = r.name
            street = ''
            role = 'AreaManager'
            street_coding = ''
            prefix = ''
            suffix = 'qu'
        else:
            street = r.name
            street_coding = r.coding
            role = 'StreetManager'
            prefix = _get_pinyin_prefix(area, ignore_char=['海淀镇', '办事处', '街道', '地区', '镇', '乡', '区',
                                                           '(', ')'])
            suffix = ''
        username = _get_pinyin(r.name, ignore_char=['海淀镇', '办事处', '街道', '地区', '镇', '乡', '区',
                                                    '(', ')'])
        username = f'{prefix}{username}{suffix}'
        manager = Manager.objects.filter(username=username).first()
        if manager:
            username = f'{username}jd'
            manager = Manager.objects.filter(username=username).first()
            if manager:
                print(r.name, username)
                break

        # 测试密码
        # password = 'zhitongtest'
        password = generate_random_number(6)
        encrypt_password = make_password(password)
        sheet.cell(row_no, 1).value = area
        sheet.cell(row_no, 2).value = street
        sheet.cell(row_no, 3).value = username
        sheet.cell(row_no, 4).value = password
        row_no += 1

        users.append(Manager(
            app_id=117998218,
            role=role,
            relation_id=get_uuid(),
            username=username,
            password=encrypt_password,
            city_coding='110000000000',
            city_name='北京市',
            area_coding=area_coding,
            area_name=area,
            street_coding=street_coding,
            street_name=street,
            comm_coding='',
            comm_name='',
            front_uri='',
            backend_uri='',
            api_uri='',
            remark='declare',
            expire_in=0,
            refresh_expire_in=0,
            is_declare=1,
            state=0,
            add_time=datetime.datetime.now()
        ))
    Manager.objects.bulk_create(users)


if __name__ == '__main__':
    template_path = './tools/declare/template.xlsx'
    excel_path = './tools/declare/北京市生活垃圾排放登记账号.xlsx'
    excel = openpyxl.load_workbook(template_path)
    sheet = excel.worksheets[0]
    row_no = 2

    # 海淀区 110108000000
    generate('110108000000')

    # 门头沟 110109000000
    generate('110109000000')

    # 房山 110111000000
    generate('110111000000')

    excel.save(excel_path)
