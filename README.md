# 排放登记管理授权中心

[[_TOC_]]

## 1. 对接说明
接入排放登记管理时需要联系管理员进行接入申请；  
申请后管理员会创建应用并出具应用相关信息及接入文档。

## 2. 应用信息
| - |说明|  
|---|---|  
| appid | 应用唯一凭证 |
| appsecret | 应用唯一凭证密钥(部分场景无需使用) |

## 3. 对接说明
管理授权目前支持
* 账号密码授权

### 3.1 返回数据
```json
{
    "code": 200,
    "msg": "处理成功.",
    "data": {
        "key1": "value1",
        "key2": value2,
        ... ... 
    }
}
```
* code明细  

| HTTP_CODE | code | msg |  
| --- | --- | --- |
| 200 | 200 | 操作成功. |
| 400 | 400 | <small>! *错误详情根据实际返回内容*</small> |
| 401 | 401 | 授权不存在或已过期. |
| 500 | 500 | 服务异常. |

### 3.2 账户密码授权
#### 3.2.1 请求授权令牌
* URL

> Method: GET  
> http://auth.ztbory.com/oauth/token?grant_type=password&appid={APPID}&username={USERNAME}&password={PASSWORD}

* 参数  

| - | 示例 | 说明 |  
| --- | --- | --- |
| grant_type | password | 认证模式：密码认证 |
| appid | {APPID} = 1000000000 | 应用唯一凭证 |
| username | {USERNAME} = manager | 管理账户用户名 |
| password | {PASSWORD} = 123456 | 管理账户密码 |

* 成功返回数据 - data

| - | 示例 | 说明 |  
| --- | --- | --- |
| token | 6a28b714014a11ebbc7e000c2971ede5 | 管理员令牌 |
| expire_in | 43200 | 用户令牌有效时长(单位:秒) |
| refresh_token | 6a2d5526014a11ebbc7e000c2971ede5 | 刷新令牌 |

> refresh_token:    
> 刷新令牌，当用户令牌即将过期或已过期时通过刷新令牌可以延长用户令牌有效时长;
> 刷新令牌有效时长为 7\*24\*60\*60 s

```json
{
    "code": 200,
    "msg": "处理成功.",
    "data": {
        "token": "6a28b714014a11ebbc7e000c2971ede5",
        "expire_in": 43200,
        "refresh_token": "6a2d5526014a11ebbc7e000c2971ede5"
    }
}
```

#### 3.2.2 令牌有效期延长
* URL

> Method: GET  
> http://auth.ztbory.com/oauth/refresh_token?appid={APPID}&refresh_token={REFRESH_TOKEN}

* 参数  

| - | 示例 | 说明 |  
| --- | --- | --- |
| appid | {APPID} = 1000000000 | 应用唯一凭证 |
| refresh_token | {REFRESH_TOKEN} = 6a2d5526014a11ebbc7e000c2971ede5 | 刷新令牌 |

* 成功返回数据 - data

| - | 示例 | 说明 |  
| --- | --- | --- |
| token | 6a28b714014a11ebbc7e000c2971ede5 | 用户令牌(会保持之前的token值) |
| expire_in | 43200 | 用户令牌有效时长(单位:秒) |
```json
{
    "code": 200,
    "msg": "处理成功.",
    "data": {
        "token": "6a28b714014a11ebbc7e000c2971ede5",
        "expire_in": 43200
    }
}
```

#### 3.2.3 获取管理员信息
* URL

> Method: GET  
> http://auth.ztbory.com/oauth/manager?appid={APPID}&token={TOKEN}

* 参数  

| - | 示例 | 说明 |  
| --- | --- | --- |
| appid | {APPID} = 1000000000 | 应用唯一凭证 |
| token | {TOKEN} = 6a28b714014a11ebbc7e000c2971ede5 | 管理员令牌 |

* 成功返回数据 - data

| - | 示例 | 说明 |  
| --- | --- | --- |
| role | StreetManager | 管理员角色类型 |
| relation_id | 434cef220ea311ebbd3890324b3e42ee | 管理授权外联内容 主键, 若无留空, 对应基本资料去相应服务获取 |
| username | manager | 管理员用户名 |
| city_coding | 110000000000 | 市级coding |
| city_name | 北京市 | 市名称 |
| area_coding | 110101000000 | 区级coding |
| area_name | 东城区 | 区名称 |
| street_coding | 110101007000 | 街道级coding |
| street_name | 朝阳门街道办事处 | 街道名称 |
| add_time | 2020-09-25 16:48:23 | 管理员账户添加时间 |

* role说明

| 值 | 说明 |  
| --- | --- |
| CityManager | 市级管理员 |
| AreaManager | 区级管理员 |
| StreetManager | 街道级管理员 |
| RenewableCollectionUnit | 再生资源收运单位 |
| RenewableCollectionDriver | 再生资源收运司机 |
| ThrowManager | 投放管理人员 |
| ExamineManager | 现场检查人员 |
| RfidMaker 	| 标签录入人员 |
| CardMaker 	| 手持开卡人员 |
| PointMaker 	| 打点人员 |
| Organization | 主体单位 |
| DutyOfficer | 桶站值守人员 |
| Terminal | 末端厂管理人员 |
| Qualification | 资质管理员 |

```
## 备用角色

市级管理员账号
区级管理员账号
街道级管理员账号
设备数据上传账号
标签录入人员账号
手持开卡人员账号
测试、开发人员账号
责任主体账号   # 包含餐饮单位，机关单位等非居民
收运公司账号
分拣公司账号
物业公司账号
收运人员账号
分拣人员账号
物业人员账号
三方数据对接账号
```


```json
{
    "code": 200,
    "msg": "处理成功.",
    "data": {
        "role": "StreetManager",
        "relation_id": "",
        "username": "chaoyangmen",
        "city_coding": "110000000000",
        "city_name": "北京市",
        "area_coding": "110101000000",
        "area_name": "东城区",
        "street_coding": "110101007000",
        "street_name": "朝阳门街道办事处",
        "add_time": "2020-09-25 16:48:23"
    }
}
```

#### 3.2.4 创建管理员
* URL

> Method: POST
> http://auth.ztbory.com/oauth/manager?appid={APPID}
> Authorization: Token {TOKEN_VALUE}

* 参数  

| - | 示例 | 说明 |  
| --- | --- | --- |
| appid | {APPID} = 1000000000 | 应用唯一凭证 |

* Header  

| - | 示例 | 说明 |  
| --- | --- | --- |
| Authorization | {TOKEN_VALUE} = 2b4622ae0eb311eb8c8a90324b3e32ee | Token令牌 |

* 成功返回数据 - data

| - | 示例 | 说明 |  
| --- | --- | --- |
| role | StreetManager | 管理员角色类型 |
| relation_id | 434cef220ea311ebbd3890324b3e42ee | 管理授权外联内容 主键, 若无留空, 对应基本资料去相应服务获取 |
| username | manager | 管理员用户名 |
| password | 123456 | 管理员密码 |
| coding | 110117005000 | 所属区域coding(非必传，默认与当前登陆管理员保持一致) |

```json
{
    "code": 200,
    "msg": "处理成功.",
    "data": {
        "role": "StreetManager",
        "relation_id": "",
        "username": "chaoyangmen",
        "city_coding": "110000000000",
        "city_name": "北京市",
        "area_coding": "110101000000",
        "area_name": "东城区",
        "street_coding": "110101007000",
        "street_name": "朝阳门街道办事处",
        "add_time": "2020-09-25 16:48:23"
    }
}
```

#### 3.2.5 修改密码
* URL

> Method: POST
> http://auth.ztbory.com/oauth/repassword?appid={APPID}
> Authorization: Token {TOKEN_VALUE}

* 参数  

| - | 示例 | 说明 |  
| --- | --- | --- |
| appid | {APPID} = 1000000000 | 应用唯一凭证 |

* Header  

| - | 示例 | 说明 |  
| --- | --- | --- |
| Authorization | {TOKEN_VALUE} = 2b4622ae0eb311eb8c8a90324b3e32ee | Token令牌 |

```json
{
    "code": 200,
    "msg": "处理成功."
}
```

#### 3.2.6 登出
* URL

> Method: POST
> http://auth.ztbory.com/oauth/logout?appid={APPID}
> Authorization: Token {TOKEN_VALUE}

* 参数  

| - | 示例 | 说明 |  
| --- | --- | --- |
| appid | {APPID} = 1000000000 | 应用唯一凭证 |

* Header  

| - | 示例 | 说明 |  
| --- | --- | --- |
| Authorization | {TOKEN_VALUE} = 2b4622ae0eb311eb8c8a90324b3e32ee | Token令牌 |

```json
{
    "code": 200,
    "msg": "处理成功."
}
```
