#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import functools
import json
import time
from typing import Any

import jwt
import requests
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.http import HttpRequest
from jwt import DecodeError
from rest_framework.permissions import BasePermission
from rest_framework.response import Response

from Base.api import ApiResponse, ConstCode
from Base.plugin import ResponseError
from OAuthApp.models import Application, Manager, ManagerJobRelation
from .licenseholder import lic


class Authorize:

    def __init__(self, request):
        """基于redis"""

        self.request = request

    def _valid_license(self):
        ok, exc = lic.verify()
        if not ok:
            return ConstCode.UnAuthorized, str(exc)
        return None, None

    def _valid_token(self):
        params = self.request.query_params.dict()
        appid = params.get('appid', '117998218')
        authorization = self.request.META.get('HTTP_AUTHORIZATION', '')
        token = authorization.replace('Token ', '').replace('token ', '').strip()
        if not token:
            token = params.get('token')

        token_value = _query_manager_by_appid_token(appid=appid, token=token)
        if not token_value:
            return ConstCode.UnAuthorized, 'Token无效或已过期.'

        relation_id = token_value.get('manager').get('relation_id')
        manager = _query_manager_by_relation_id(relation_id)
        if not manager or manager.state == 1:
            return ConstCode.UnAuthorized, '账户已禁用.'

        salt = token_value.get('manager').get('salt')
        if salt and salt != manager.salt:
            return ConstCode.UnAuthorized, '令牌已过期,请重新登陆.'

        token_expire = cache.ttl('Token:{}'.format(token))

        self.request.appid = appid
        self.request.token = token
        self.request.token_value = token_value
        self.request.token_manager = manager
        self.request.token_expire = token_expire
        return None, None

    def valid(self):
        code, msg = self._valid_license()
        if code and msg:
            return code, msg

        code, msg = self._valid_token()
        if code and msg:
            return code, msg
        return None, None

    def valid_with_relations(self):
        code, msg = self.valid()
        if code and msg:
            return code, msg

        relations = ManagerJobRelation.objects.filter(manager_id=self.request.token_manager.id).all()
        if not relations:
            return ConstCode.UnAuthorized, '无效的账户（无职务信息）.'

        manager_relation = dict(
            agency_id=relations[0].agency_id,
            department_id=relations[0].department_id,
            is_sys=relations[0].is_sys,
        )
        self.request.token_manager_job_relation = manager_relation
        return None, None


class JWTAuthorize:
    def __init__(self, request):
        """基于jwt"""

        self.request = request

    def _valid_license(self):
        ok, exc = lic.verify()
        if not ok:
            return ConstCode.UnAuthorized, str(exc)
        return None, None

    def _valid_token(self):
        params = self.request.query_params.dict()
        appid = params.get('appid', '117998218')
        authorization = self.request.META.get('HTTP_AUTHORIZATION', '')
        token = authorization.replace('Token ', '').replace('token ', '').strip()
        if not token:
            token = params.get('token')

        try:
            payload = jwt.decode(token, key=settings.SECRET_KEY, algorithms=['HS256'])
            username = payload['username']
            expired_at = payload['exp']
            ttl = int(expired_at - time.time())
            manager = Manager.objects.filter(app_id=appid, username=username).get()
        except (jwt.ExpiredSignatureError, DecodeError, ObjectDoesNotExist, KeyError):
            return ConstCode.UnAuthorized, 'Token无效或已过期.'

        if not manager or manager.state == 1:
            return ConstCode.UnAuthorized, '账户已禁用.'

        self.request.appid = appid
        self.request.token = token
        self.request.token_manager = manager
        self.request.token_expire = ttl
        return None, None

    def valid(self):
        code, msg = self._valid_license()
        if code and msg:
            return code, msg

        code, msg = self._valid_token()
        if code and msg:
            return code, msg
        return None, None

    def valid_with_relations(self):
        code, msg = self.valid()
        if code and msg:
            return code, msg

        relations = ManagerJobRelation.objects.filter(manager_id=self.request.token_manager.id).all()
        if not relations:
            return ConstCode.UnAuthorized, '无效的账户（无职务信息）.'

        manager_relation = dict(
            agency_id=relations[0].agency_id,
            department_id=relations[0].department_id,
            is_sys=relations[0].is_sys,
        )
        self.request.token_manager_job_relation = manager_relation
        return None, None


def token_required(function):
    def _wrapper(request):
        authorize = JWTAuthorize(request)
        code, msg = authorize.valid()
        if code and msg:
            return ApiResponse(code=code, msg=msg)
        return function(authorize.request)

    return _wrapper


def token_required_or_implicit(function):
    """token或隐式"""

    def _wrapper(request):
        ok, exc = lic.verify()
        if not ok:
            return ApiResponse(code=ConstCode.UnAuthorized, msg=str(exc))

        params = request.query_params.dict()
        appid = params.get('appid')
        appsecret = params.get('appsecret')
        if appid and appsecret:
            application = Application.objects.filter(app_id=appid,
                                                     app_secret=appsecret,
                                                     state=0).first()
            if not application:
                return ApiResponse(code=ConstCode.BadRequest, msg='无效的AppId或AppSecret.')
            request.appid = appid
            request.token_type = 'implicit'
        else:
            authorize = JWTAuthorize(request)
            code, msg = authorize.valid()
            if code and msg:
                return ApiResponse(code=code, msg=msg)
            request.token_type = 'token'

        return function(request)

    return _wrapper


def _query_manager_by_relation_id(relation_id):
    return Manager.objects.filter(relation_id=relation_id).first()


def _query_manager_by_appid_token(appid, token):
    token_value = cache.get('Token:{}'.format(token))
    if not token_value or str(token_value.get('appid', '')) != appid:
        return None
    return token_value


class GeneralPermission(BasePermission):

    def has_permission(self, request, view):
        authorize = JWTAuthorize(request)
        code, msg = authorize.valid()
        if code and msg:
            raise ResponseError(const_code=code, detail=msg)
        return True


class SitePermission(BasePermission):

    def has_permission(self, request, view):
        authorize = JWTAuthorize(request)
        code, msg = authorize.valid_with_relations()
        if code and msg:
            raise ResponseError(const_code=code, detail=msg)
        return True


def login(request):
    ok, exc = lic.verify()
    if not ok:
        raise ResponseError(const_code=ConstCode.UnAuthorized, detail=str(exc))

    from OAuthApp.services import apply_token
    return apply_token(request)


def get_request_json(request: HttpRequest) -> Any:
    try:
        return json.loads(request.body)
    except Exception:
        return None


def gen_jwt_token(manager: Manager, expires_in: int):
    exp = int(time.time()) + expires_in
    payload = dict(appid=manager.app_id, username=manager.username, exp=exp)
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')


def gen_jwt_refresh_token(manager: Manager, refresh_expires_in: int):
    exp = int(time.time()) + refresh_expires_in
    payload = dict(refresh_appid=manager.app_id, refresh_username=manager.username, exp=exp)
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')


def get_proxy_response(request):
    if not settings.PROXY_ENABLED:
        return None

    method = request.method
    path = request.path_info
    url = f"{settings.PROXY_HOST}{path.replace('/oauth', '')}"
    params = request.query_params.dict()
    data = request.POST.dict() or None
    json_data = get_request_json(request)
    headers = {
        "Authorization": request.META.get('HTTP_AUTHORIZATION', ''),
        "X-Proxy-Through": "yes"
    }
    response = requests.request(method, url, params=params, data=data, json=json_data, headers=headers)
    try:
        result = response.json()
    except Exception:
        result = response.content.decode('utf-8')
    return Response(result, status=response.status_code)


def proxy_request(with_class=False):
    def wrapper(function):
        if with_class:
            @functools.wraps(function)
            def inner_wrapper(self, request: HttpRequest, *args, **kwargs):
                if not settings.PROXY_ENABLED:
                    return function(self, request, *args, **kwargs)
                return get_proxy_response(request)

            return inner_wrapper
        else:
            @functools.wraps(function)
            def inner_wrapper(request: HttpRequest, *args, **kwargs):
                if not settings.PROXY_ENABLED:
                    return function(request, *args, **kwargs)
                return get_proxy_response(request)

            return inner_wrapper

    return wrapper
