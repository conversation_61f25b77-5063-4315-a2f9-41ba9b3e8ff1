#!/home/<USER>/vevn/bin/python
# coding: utf-8
from __future__ import absolute_import

from django.forms import model_to_dict
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from Base.plugin import <PERSON><PERSON><PERSON>erial<PERSON>Field
from Base.utils import validate_password
from .models import OperationLog, Navigation, Agency, AgencyType, Department, Job, Site, Manager, ManagerJobRelation, \
    CityOperationOnLog


class ManagerRegisterSerializer(serializers.Serializer):
    role = serializers.CharField(max_length=56)
    relation_id = serializers.Char<PERSON>ield(max_length=32)
    username = serializers.Char<PERSON>ield(min_length=4, max_length=36)
    password = serializers.CharField(min_length=6, max_length=32)
    realname = serializers.CharField(default="")

    coding = serializers.CharField(required=False)

    expire_in = serializers.IntegerField(default=0)
    refresh_expire_in = serializers.IntegerField(default=0)
    state = serializers.IntegerField(default=0)

    extra_configure = serializers.JSONField(default=dict)


class ManagerFakeRegionSerializer(serializers.Serializer):
    coding = serializers.CharField()


class ManagerRepasswordSerializer(serializers.Serializer):
    password = serializers.CharField(allow_blank=False, allow_null=False)
    new_password = serializers.CharField(min_length=8, max_length=32)

    def validate_new_password(self, value):
        if not validate_password(value):
            raise ValidationError({'new_password': '8-32位字符，要求同时具备英文大写、小写、阿拉伯数字'})
        return value


class ManagerChangeMyPasswordSerializer(serializers.Serializer):
    password = serializers.CharField(min_length=8, max_length=32)

    def validate_password(self, value):
        if not validate_password(value):
            raise ValidationError({'new_password': '8-32位字符，要求同时具备英文大写、小写、阿拉伯数字'})
        return value


class ManagerLogoffSerializer(serializers.Serializer):
    state = serializers.IntegerField(default=0)


class ManagerResetPasswordSerializer(serializers.Serializer):
    password = serializers.CharField(min_length=6, max_length=32)

    def validate_password(self, value):
        if not validate_password(value):
            raise ValidationError({'password': '8-32位字符，要求同时具备英文大写、小写、阿拉伯数字'})
        return value


class ManagerResetPasswordSimpleSerializer(serializers.Serializer):
    password = serializers.CharField(min_length=6, max_length=32)


class ManagerResetUsernameSimpleSerializer(serializers.Serializer):
    username = serializers.CharField(max_length=56)


class ManagerLoginLogSer(serializers.Serializer):
    relation_id = serializers.CharField(max_length=32)
    ip = serializers.CharField(max_length=255)
    username = serializers.CharField(max_length=56)
    login_type = serializers.IntegerField()
    content = serializers.CharField(max_length=255)
    # create_time = serializers.DateTimeField(read_only=True)
    is_deleted = serializers.IntegerField(read_only=True)

    def create(self, validated_data):
        """
        :param validated_data: 校验之后的数据
        :return:
        """
        obj = OperationLog.objects.create(**validated_data)
        return obj

    create_time = serializers.SerializerMethodField()

    def get_create_time(self, obj):
        create_time = obj.create_time.strftime('%Y-%m-%d %H:%M:%S')
        return create_time


class ManagerSer(serializers.Serializer):
    role = serializers.CharField(max_length=56)
    relation_id = serializers.CharField(max_length=56)
    username = serializers.CharField(max_length=56)
    city_coding = serializers.CharField(max_length=12)
    city_name = serializers.CharField(max_length=56)
    area_coding = serializers.CharField(max_length=12)
    area_name = serializers.CharField(max_length=56)
    street_coding = serializers.CharField(max_length=12)
    street_name = serializers.CharField(max_length=56)
    comm_coding = serializers.CharField(max_length=12)
    comm_name = serializers.CharField(max_length=56)
    remark = serializers.CharField(max_length=255)
    state = serializers.IntegerField()
    add_time = serializers.DateTimeField()


class SiteSer(serializers.ModelSerializer):
    state_name = serializers.CharField(source='get_state_display', read_only=True)

    class Meta:
        model = Site
        fields = '__all__'


class NavigationSer(serializers.ModelSerializer):
    type_name = serializers.CharField(source='get_type_display', read_only=True)
    state_name = serializers.CharField(source='get_state_display', read_only=True)

    def validate(self, attrs):
        # 目录 组件名称不做限制
        # 菜单 组件名不可重复
        if attrs.get('type') == 'MENU':
            check_component = Navigation.objects.filter(
                site_id=attrs.get('site_id'),
                component_name=attrs.get('component_name')
            )
            if self.instance:
                check_component = check_component.exclude(id=self.instance.id)
            if check_component.exists():
                raise ValidationError({'component_name': '同一站点的组件名称不可重复.'})
        # 按钮 权限标识不可重复
        elif attrs.get('type') == 'BUTTON':
            check_permission = Navigation.objects.filter(
                site_id=attrs.get('site_id'),
                permission=attrs.get('permission')
            )
            if self.instance:
                check_permission = check_permission.exclude(id=self.instance.id)
            if check_permission.exists():
                raise ValidationError({'component_name': '同一站点的按钮权限标识不可重复.'})
        return attrs

    class Meta:
        model = Navigation
        fields = '__all__'


class AgencyTypeSer(serializers.ModelSerializer):
    state_name = serializers.CharField(source='get_state_display', read_only=True)

    class Meta:
        model = AgencyType
        fields = '__all__'


class AgencySer(serializers.ModelSerializer):
    agency_type_id = serializers.ReadOnlyField(source='agency_type.id')
    agency_type_name = serializers.ReadOnlyField(source='agency_type.name')
    state_name = serializers.CharField(source='get_state_display', read_only=True)
    auth_configure = JsonSerializerField()
    is_leaf = serializers.IntegerField(read_only=True)

    class Meta:
        model = Agency
        exclude = ('parent_map',)
        # fields = '__all__'


class DepartmentSer(serializers.ModelSerializer):
    agency_id = serializers.PrimaryKeyRelatedField(
        queryset=Agency.objects.all(),
        error_messages={'does_not_exist': "组织机构不存在."}
    )
    state_name = serializers.CharField(source='get_state_display', read_only=True)

    class Meta:
        model = Department
        fields = '__all__'


class JobSer(serializers.ModelSerializer):
    agency_id = serializers.PrimaryKeyRelatedField(
        queryset=Agency.objects.all(),
        error_messages={'does_not_exist': "组织机构不存在."}
    )
    department_id = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.all(),
        error_messages={'does_not_exist': "部门不存在."}
    )
    state_name = serializers.CharField(source='get_state_display', read_only=True)

    class Meta:
        model = Job
        exclude = ('sync_to_soyuan',)


class StaffSer(serializers.ModelSerializer):
    username = serializers.CharField(min_length=4, max_length=36, error_messages={
        'required': '请提供用户名.',
        'invalid': '无效的用户名.',
        'min_length': '用户名最短4位.',
        'max_length': '用户名最长36位.'
    })
    agency_id = serializers.IntegerField(write_only=True, error_messages={
        'required': '请选中所属机构.'
    })
    job_ids = serializers.ListField(write_only=True, error_messages={
        'required': '请选中所属职务.'
    })
    is_sys = serializers.IntegerField(min_value=0, max_value=1, write_only=True, error_messages={
        'required': '请选择是否为系统管理员.',
        'invalid': '请选择是否为系统管理员.',
        'min_value': '请选择是否为系统管理员.',
        'max_string_length': '请选择是否为系统管理员.'
    })
    add_time = serializers.DateTimeField(read_only=True)
    password = serializers.CharField(write_only=True)
    state_name = serializers.CharField(source='get_state_display', read_only=True)

    def validate_password(self, value):
        if not validate_password(value):
            raise ValidationError('8-32位字符，要求同时具备英文大写、小写、阿拉伯数字')
        return value

    def _cache_function(self, func, *args):
        if not hasattr(self, '__cache__'):
            self.__cache__ = dict()

        func_name = func.__name__
        if func_name not in self.__cache__:
            self.__cache__[func_name] = dict()

        key = tuple(args)
        if key in self.__cache__[func_name]:
            return self.__cache__[func_name].get(key)

        value = func(*args)
        self.__cache__[func_name][key] = value

        return value

    @staticmethod
    def _query_agency(agency_id):
        agency = Agency.objects.filter(id=agency_id).first()
        return model_to_dict(agency) if agency else {}

    @staticmethod
    def _query_department(department_id):
        department = Department.objects.filter(id=department_id).first()
        return model_to_dict(department) if department else {}

    @staticmethod
    def _query_job(job_id):
        job = Job.objects.filter(id=job_id).first()
        return model_to_dict(job) if job else {}

    @staticmethod
    def _query_siblings_agency(parent_id):
        siblings = Agency.objects.filter(parent_id=parent_id).exclude(id=1).all()
        return [model_to_dict(obj) for obj in siblings]

    def to_representation(self, obj):
        request = self.context.get('request')
        manager_job_relation = request.token_manager_job_relation
        data = super().to_representation(obj)
        relations = ManagerJobRelation.objects.filter(manager_id=data.get('id')).all()
        agency_name = set()
        department_job_name = set()
        jobs = []
        current_id = 0
        for rel in relations:
            agency = self._cache_function(self._query_agency, rel.agency_id)
            current_id = agency.get('id')
            data['agency_id'] = current_id
            agency_name.add(agency.get('name'))
            department = self._cache_function(self._query_department, rel.department_id)
            data['department_id'] = department.get('id', 0)
            data['department_name'] = department.get('name', '系统')
            job = self._cache_function(self._query_job, rel.job_id)
            data['is_sys'] = rel.is_sys
            department_job_name.add(f"{department.get('name', '系统')}-{job.get('name', '管理员')}")
            jobs.append(dict(
                agency_id=agency['id'] if agency else 0,
                agency_name=agency['name'] if agency else "",
                department_id=department.get('id', 0),
                department_name=department.get('name', '系统'),
                job_id=job.get('id', 0),
                job_name=job.get('name', '管理员'),
                is_sys=job.get('is_sys', 1)
            ))

        agency_for_selection = []
        while True:
            agency = self._cache_function(self._query_agency, current_id)
            if not agency:
                break

            # 机构截止到当前登陆用户机构
            if agency['id'] == manager_job_relation['agency_id']:
                agency_for_selection.append(dict(
                    id=agency['id'],
                    parent_id=agency['parent_id'],
                    name=agency['name'],
                    is_leaf=agency['is_leaf'],
                    is_selected=1,
                ))
                break

            parent_id = agency.get('parent_id')
            siblings = self._cache_function(self._query_siblings_agency, parent_id)

            for sbl in siblings:
                sbl = dict(
                    id=sbl['id'],
                    parent_id=sbl['parent_id'],
                    name=sbl['name'],
                    is_leaf=sbl['is_leaf'],
                    is_selected=0,
                )

                if sbl.get('id') == current_id:
                    sbl['is_selected'] = 1
                    current_id = sbl.get('id')
                else:
                    sbl['is_selected'] = 0

                agency_for_selection.append(sbl)

            current_id = parent_id

            if not current_id:
                break

        data['agency_name'] = ';'.join(agency_name)
        data['department_job_name'] = ';'.join(department_job_name)
        data['jobs'] = jobs
        data['agency_for_selection'] = agency_for_selection
        return data

    class Meta:
        model = Manager
        exclude = (
            'app_id', 'role', 'relation_id', 'salt',
            'city_coding', 'city_name',
            'area_coding', 'area_name',
            'street_coding', 'street_name',
            'comm_coding', 'comm_name',
            'front_uri', 'backend_uri', 'api_uri', 'remark', 'expire_in', 'refresh_expire_in', 'is_declare'
        )


class JobStaffSer(serializers.Serializer):
    job_id = serializers.IntegerField(write_only=True, error_messages={
        'required': '请选中所属职务.'
    })
    manager_ids = serializers.ListField(write_only=True, error_messages={
        'required': '请选中要添加的管理员.'
    })


class AgencyTypeRoleSettingsCreateSer(serializers.Serializer):
    navigation_ids = serializers.ListField(error_messages={'required': '未配置任何权限.'})


class JobRoleSettingsCreateSer(serializers.Serializer):
    navigation_ids = serializers.ListField(error_messages={'required': '未配置任何权限.'})


class CityOperationOnLogSer(serializers.ModelSerializer):
    class Meta:
        model = CityOperationOnLog
        fields = '__all__'
