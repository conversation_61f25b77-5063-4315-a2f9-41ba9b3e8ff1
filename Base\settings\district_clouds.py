#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *
from .clouds_environment import *

DEBUG = False
ENVIRONMENT = 'production'

# 数智源数据同步接口
SOYUAN_SYNC_HOST = '************:8002'
DATAV_SYNC_HOST = f'http://{ENV_PROXY_IP}/inskip/waste-management'
# 数智源同步任务开启状态
SYNC_SCHEDULER_ON = False
SYNC_SCHEDULER_RECENTLY = True

# Proxy
JWT_ENABLED = False
PROXY_ENABLED = True
PROXY_HOST = 'https://api.ztbory.com/v3/auth'

# 密码错误防护
# 最多错误次数
PASSWORD_INCORRECT_TIMES = 6
# 达到次数禁用时长 s
PASSWORD_INCORRECT_DISABLE_DURATION = 60 * 60 * 24
PASSWORD_INCORRECT_TIPS = "24小时"

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3'
    },
    'ljfl_auth_manager_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_auth_manager_db',
        'USER': ENV_DB_USER,
        'HOST': ENV_DB_HOST,
        'PASSWORD': ENV_DB_PASSWORD,
        'PORT': ENV_DB_PORT,
        'OPTIONS': {'charset': 'utf8'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': ENV_DB_USER,
        'HOST': ENV_DB_HOST,
        'PASSWORD': ENV_DB_PASSWORD,
        'PORT': ENV_DB_PORT,
        'OPTIONS': {'charset': 'utf8'}
    }
}

# redis配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'KEY_PREFIX': 'api:ljflAuthManager',
        'LOCATION': f'redis://{ENV_REDIS_HOST}:{ENV_REDIS_PORT}/4',
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": ENV_REDIS_PASSWORD,
        },
    },
}
