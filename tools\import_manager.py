#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import os
import xlrd
import datetime
import django
from django.contrib.auth.hashers import make_password

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
django.setup()

from OAuthApp.models import Manager


def import_manager():
    excel = xlrd.open_workbook('./tools/import_manager/chaoyang.xlsx')
    sheet = excel.sheet_by_name('Sheet1')
    for i, row in enumerate(sheet.get_rows()):
        if i == 0:
            continue
        area_name, street_name, coding, username, password, front, backend, api, _, remark = [
            itm.value for itm in row
        ]

        city_coding = '110000000000'
        city_name = '北京市'
        if coding.endswith('000000'):
            area_coding, street_coding = coding, ''
            street_name = ''
            remark = ''
        else:
            street_coding = coding
            area_coding = coding[:6] + '000000'

        password = make_password(password)
        Manager.objects.create(app_id=117998270,
                               username=username,
                               password=password,
                               city_coding=city_coding,
                               city_name=city_name,
                               area_coding=area_coding,
                               area_name=area_name,
                               street_coding=street_coding,
                               street_name=street_name,
                               front_uri=front,
                               backend_uri=backend,
                               api_uri=api,
                               remark=remark,
                               expire_in=0,
                               refresh_expire_in=0,
                               state=0,
                               add_time=datetime.datetime.now())


if __name__ == '__main__':
    import_manager()
