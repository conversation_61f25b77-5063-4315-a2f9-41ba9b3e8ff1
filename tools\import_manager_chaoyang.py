#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import os
import datetime
import django
from Base.utils import get_uuid
import xlrd
from django.contrib.auth.hashers import make_password

# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.import")
django.setup()

from OAuthApp.models import Manager
from Base.utils.coding import get_coding_name


# manager = Manager()
# manager.set_password("Ztbory304%")
# print(manager.password)
# print(manager.encrypted_password)
# exit()


def import_manager():
    excel = xlrd.open_workbook('./tools/import_manager/Shunyi_0704.xlsx')
    sheet = excel.sheet_by_name('Sheet1')
    for i, row in enumerate(sheet.get_rows()):
        if i == 0:
            continue
        area_name, street_name, coding, username, password, front, backend, api, _, remark, role = [
            itm.value for itm in row
        ]
        username = str(username).replace(".0", "")

        check = Manager.objects.filter(username=username).first()  # type: Manager
        if check:
            print(f'username:{username} already exist.')
            check.set_password(password)
            check.save()
            continue

        city_coding = '110000000000'
        city_name = '北京市'
        comm_coding, comm_name = '', ''
        if coding.endswith('000000'):
            area_coding, street_coding = coding, ''
            street_name = ''
            remark = ''
        elif coding.endswith('000'):
            street_coding = coding
            area_coding = coding[:6] + '000000'
        else:
            area_coding = coding[:6] + '000000'
            street_coding = coding[:9] + '000'
            comm_coding = coding

        area_name = get_coding_name(area_coding)
        street_name = get_coding_name(street_coding)
        comm_name = get_coding_name(comm_coding)
        realname = comm_name or street_name or area_name

        manager = Manager(
            app_id=117998218,
            role=role,
            relation_id=get_uuid(),
            username=username,
            realname=realname,
            city_coding=city_coding,
            city_name=city_name,
            area_coding=area_coding,
            area_name=area_name,
            street_coding=street_coding,
            street_name=street_name,
            comm_coding=comm_coding,
            comm_name=comm_name,
            front_uri=front,
            backend_uri=backend,
            api_uri=api,
            remark=remark,
            expire_in=0,
            refresh_expire_in=0,
            is_declare=1,
            state=0,
            add_time=datetime.datetime.now()
        )
        manager.set_password(password)
        manager.save()


if __name__ == '__main__':
    import_manager()
