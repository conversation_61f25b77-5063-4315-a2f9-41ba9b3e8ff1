#!/usr/bin/env python
import os
import sys
import socket

if __name__ == '__main__':
    environment = 'production'
    hostname = socket.gethostname()
    if hostname in ['Server-61198fa2-11ab-4fd6-8c45-cd138a1534fe.novalocal']:
        environment = 'testing'

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Base.settings.{}'.format(environment))
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)
