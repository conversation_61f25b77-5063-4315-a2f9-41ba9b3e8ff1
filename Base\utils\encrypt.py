import binascii

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad


class PasswordCryptor:
    def __init__(self):
        # 密钥必须为16/24/32位
        self.key = 'Ztbory.__.yrobtZ'.encode()

        self.mode = AES.MODE_ECB
        self.cryptor = AES.new(self.key, self.mode)

    def encrypt(self, plain_text):
        encrypted_text = self.cryptor.encrypt(pad(plain_text.encode('utf-8'), AES.block_size))
        return encrypted_text

    def decrypt(self, encrypted_text):
        plain_text = self.cryptor.decrypt(encrypted_text)
        plain_text = unpad(plain_text, AES.block_size).decode()
        return plain_text


password_cryptor = PasswordCryptor()

if __name__ == '__main__':
    pc = PasswordCryptor()
    e = pc.encrypt("0123456789ABCDEF中国0123456789ABCDEF中国0123456789ABCDEF中国0123456789ABCDEF中国")
    d = pc.decrypt(e)
    print(e, d)
    print(e.hex())
    b = binascii.a2b_hex('fb54e92e85e71e808b79cf42c4917441')
    d = pc.decrypt(b)
    print(d)
