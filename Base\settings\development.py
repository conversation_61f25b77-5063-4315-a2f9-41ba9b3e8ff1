#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *

DATABASES = {
    'default': {
    },
    'ljfl_auth_manager_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_auth_manager_db',
        'USER': 'root',
        'HOST': '**********',
        'PASSWORD': 'zhitongbr2019',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'root',
        'HOST': '************',
        'PASSWORD': 'zhitongbr@2020',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8'}
    }
}

JWT_ENABLED = True
PROXY_ENABLED = False
PROXY_HOST = ''

# # redis配置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://**********:6379/4",
        'KEY_PREFIX': 'apiTesting:ljflAuthManager',
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
        }
    }
}
