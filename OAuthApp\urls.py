#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from django.urls import path, include, re_path
from rest_framework import routers
from . import views

router = routers.DefaultRouter()
# api - 权限管理 市级相关
router.register('site', views.SiteView)
router.register('navigation', views.NavigationView)
router.register('agency_type', views.AgencyTypeView)
router.register('agency', views.AgencyView)
router.register('department', views.DepartmentView)
router.register('job', views.JobView)
router.register('staff', views.StaffView)

urlpatterns = [
    path('', include(router.urls)),

    # api - 授权中心
    path('token', views.TokenView.as_view()),
    path('manager', views.ManagerView.as_view()),
    path('refresh_token', views.RefreshTokenView.as_view()),
    path('repassword', views.RepasswordView.as_view()),
    path('change-my-password', views.ChangeMyPasswordView.as_view()),
    path('resetpassword', views.ResetPasswordView.as_view()),
    path('resetusername', views.ResetUsernameView.as_view()),
    path('logout', views.LogoutView.as_view()),

    path('manager/logoff', views.ManagerLogoffView.as_view()),
    path('operation_log', views.ManagerOperationLogView.as_view()),
    path('city_departments', views.CityOperationOnLogDepartmentView.as_view()),

    # 获取账号列表
    path('account_list', views.AccountListfView.as_view()),

    # api - 权限管理
    # 职务添加管理员
    path('job_staff/', views.JobStaffView.as_view()),

    re_path(r'agency_type/(?P<agency_type_id>\d+)/settings/', views.AgencyTypeRoleSettingsView.as_view()),
    re_path(r'job/(?P<job_id>\d+)/settings/', views.JobRoleSettingsView.as_view()),

    # 当前账号获取导航菜单
    path('manager/navigation/', views.ManagerNavigationView.as_view()),
]
