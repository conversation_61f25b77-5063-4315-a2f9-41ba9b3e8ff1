#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import json
import logging
from collections import OrderedDict
from django.shortcuts import HttpResponse
from django.utils.deprecation import MiddlewareMixin
from rest_framework import status
from rest_framework.request import Request, QueryDict
from rest_framework.utils.serializer_helpers import ReturnDict
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from . import plugin
from .plugin import ResponseError


class ConstRole:
    """
    授权账户角色
    """
    FakeManager = 'FakeManager'  # 可自由切换的管理员角色

    CityManager = 'CityManager'  # 市级管理员
    AreaManager = 'AreaManager'  # 区级管理员
    StreetManager = 'StreetManager'  # 街道级管理员
    CommManager = 'CommManager'  # 社区级管理员
    RenewableCollectionUnit = 'RenewableCollectionUnit'  # 再生资源收运单位
    RenewableCollectionDriver = 'RenewableCollectionDriver'  # 再生资源收运司机
    ThrowManager = 'ThrowManager'  # 投放管理人员
    ExamineManagerAdmin = 'ExamineManagerAdmin'  # 现场检查管理人员
    ExamineManager = 'ExamineManager'  # 现场检查人员
    RfidMaker = 'RfidMaker'  # 标签录入人员
    CardMaker = 'CardMaker'  # 手持开卡人员
    PointMaker = 'PointMaker'  # 打点人员
    Replacementer = 'Replacementer'  # 补卡人员
    Organization = 'Organization'  # 主体单位
    DutyOfficer = 'DutyOfficer'  # 值守人员（垃圾桶）
    RectifyDutyOfficer = 'RectifyDutyOfficer'  # 桶站值守(检查单整改人员)

    Terminal = 'Terminal'  # 末端厂管理员

    Collector = 'Collector'  # 上门回收人员
    ThrowAndCollector = 'ThrowAndCollector'  # 投放和回收人员

    # 再生资源
    Collection = 'Collection'  # 收运公司()
    CollectionDriver = 'CollectionDriver'  # 收运公司司机

    # 非居民责任主体计费系统
    SocialOrganization = 'SocialOrganization'  # 社会责任主体(非居民责任主体)
    # 之前资质审核都是收运公司，两个共用
    Qualification = 'Qualification'  # 资质管理员(收运公司)
    QualificationDriver = 'QualificationDriver'  # 司机(收运公司-车辆)

    # 十里堡监督执法人员
    Supervision = 'Supervision'  # 监督执法人员

    # 台账申报人员
    Declarantor = 'Declarantor'  # 台账申报人员

    # 入户宣传人员
    Publicists = 'Publicists'  # 入户宣传人员

    # 李振再生资源交易平台
    RenewableResourcesSuper = 'RenewableResourcesSuper'  # 超级管理员
    RenewableResourcesTenant = 'RenewableResourcesTenant'  # 租户
    RenewableResourcesUser = 'RenewableResourcesUser'  # 用户
    RenewableResourcesApplet = 'RenewableResourcesApplet'  # 小程序

    # 物联网管理员
    DeviceManagerAdmin = 'DeviceManagerAdmin'  # 物联网超级用户（最高级别）
    DeviceCityManager = 'DeviceCityManager'  # 物联网管理员

    __map__ = {
        FakeManager: '管理员',

        CityManager: '市级管理员',
        AreaManager: '区级管理员',
        StreetManager: '街道级管理员',
        CommManager: '社区级管理员',

        # 再生资源
        RenewableCollectionUnit: '再生资源收运单位',
        RenewableCollectionDriver: '再生资源收运司机',

        # 投放管理人员
        ThrowManager: '投放管理人员',

        # 现场检查人员 维护人员 - PC进行检查人员管理
        ExamineManagerAdmin: '现场检查管理人员',

        # 现场检查人员
        ExamineManager: '现场检查人员',

        # 标签录入人员
        RfidMaker: '标签录入人员',

        # 手持开卡人员
        CardMaker: '手持开卡人员',

        # 补卡人员
        Replacementer: '补卡人员',

        # 打点人员
        PointMaker: '打点人员',

        # 主体单位
        Organization: '主体单位',

        # 值守人员（垃圾桶）
        DutyOfficer: '值守人员',

        # 桶站值守(检查单整改人员)
        RectifyDutyOfficer: '检查单整改人员',

        # 末端厂管理员
        Terminal: '末端厂管理员',

        # 上门回收人员
        Collector: '上门回收人员',

        # 投放和回收人员
        ThrowAndCollector: '投放和回收人员',

        # 再生资源
        # 收运公司 - 收运公司资质管理
        Collection: '收运公司',
        # 收运公司司机
        CollectionDriver: '收运公司司机',

        # 非居民责任主体计费系统
        # 社会责任主体 - 非居民责任主体
        SocialOrganization: '非居民责任主体',
        Qualification: '收运公司资质管理员',
        QualificationDriver: '收运公司司机人员',

        # 十里堡监督执法人员
        Supervision: '监督执法人员',

        # 台账申报人员
        Declarantor: '台账申报人员',

        # 入户宣传人员
        Publicists: '入户宣传人员',

        # 李振再生资源交易平台
        RenewableResourcesSuper: '超级管理员',
        RenewableResourcesTenant: '租户',
        RenewableResourcesUser: '用户',
        RenewableResourcesApplet: '小程序用户',
    }

    def get_name(self, role):
        return self.__map__.get(role)


class ConstCode:
    """
    返回码
    """
    Success = (status.HTTP_200_OK, 200, '处理成功.')

    Created = (status.HTTP_201_CREATED, 200, '创建成功.')

    Deleted = (status.HTTP_204_NO_CONTENT, 200, '删除成功.')

    BadRequest = (status.HTTP_400_BAD_REQUEST, 400, '提交的数据有误.')
    Existed = (status.HTTP_400_BAD_REQUEST, 400, '对象已存在.')
    NotExisted = (status.HTTP_400_BAD_REQUEST, 400, '对象不存在.')

    UnAuthorized = (status.HTTP_401_UNAUTHORIZED, 401, '认证失败.')

    Forbidden = (status.HTTP_400_BAD_REQUEST, 400, '无法访问.')
    NotAllowed = (status.HTTP_400_BAD_REQUEST, 400, '无权限.')

    NotFound = (status.HTTP_404_NOT_FOUND, 404, '资源不存在.')

    MethodNotAllowed = (status.HTTP_405_METHOD_NOT_ALLOWED, 405, '不支持该操作.')

    TooManyRequests = (status.HTTP_429_TOO_MANY_REQUESTS, 429, '请求过于频繁, 请稍候重试.')

    ServerError = (status.HTTP_500_INTERNAL_SERVER_ERROR, 500, '服务错误.')

    __map__ = {
        status.HTTP_200_OK: '处理成功.',
        # status.HTTP_201_CREATED: '创建成功.',
        # status.HTTP_204_NO_CONTENT: '删除成功.',
        status.HTTP_400_BAD_REQUEST: '提交的数据有误.',
        status.HTTP_401_UNAUTHORIZED: '认证失败.',
        status.HTTP_403_FORBIDDEN: '无法访问.',
        status.HTTP_404_NOT_FOUND: '资源不存在.',
        status.HTTP_405_METHOD_NOT_ALLOWED: '不支持该操作.',
        status.HTTP_429_TOO_MANY_REQUESTS: '请求过于频繁，请稍候重试.',
        status.HTTP_500_INTERNAL_SERVER_ERROR: '服务错误.',
    }

    def get_default_msg(self, http_status_code):
        return self.__map__.get(http_status_code, '处理成功.')


ConstCode = ConstCode()


def ApiResponse(code=ConstCode.Success, msg=None, data=None, errors=None):
    """
    Api返回内容
    :param code:
    :type code: ConstCode
    :param msg:
    :param data:
    :param errors:
    :return:
    """
    http_status_code, response_code, response_msg = code
    if msg:
        response_msg = msg
    response = dict(code=response_code, msg=response_msg)
    key = 'data'
    if http_status_code >= 300:
        key = 'errors'
    if data:
        response[key] = data
    if errors:
        response['errors'] = errors
        response['msg'] = '; '.join(list(errors.values())[0])

    return Response(data=response, status=http_status_code)


class ApiJsonRender(JSONRenderer):
    """rest-framework 自带方法结果调整"""

    # 重构render方法
    def render(self, data, accepted_media_type=None, renderer_context=None):
        if (not data or 'code' not in data) and renderer_context:
            status_code = renderer_context.get('response').status_code
            if 200 < status_code < 300:
                data = None
                status_code = 200
            # status_text = renderer_context.get('response').status_text
            status_text = ConstCode.get_default_msg(status_code)
            key = 'data'
            if status_code >= 300:
                key = ''
                if isinstance(data, ReturnDict):
                    key = 'errors'
            # 重新构建返回的JSON字典
            ret = {
                'msg': status_text,
                'code': status_code,
            }
            if key and (data or data == list()):
                ret[key] = data
                if key == 'errors':
                    ret['msg'] = '; '.join(list(data.values())[0])

            # 返回JSON数据
            logger.info('[RESPONSE] %s', ret)
            return super().render(ret, accepted_media_type, renderer_context)
        else:
            logger.info('[RESPONSE] %s', data)
            return super().render(data, accepted_media_type, renderer_context)


class ApiPageNumberPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('results', data)
        ]))


def get_logger(logger_name='django'):
    """
    获取日志句柄
    :param logger_name:
    :return:
    """
    return logging.getLogger(logger_name)


# 项目默认日志
logger = get_logger()


class ApiMiddleWare(MiddlewareMixin):
    """Api中间件"""

    @staticmethod
    def process_request(request):
        content_type = request.content_type
        logger.info('[URL] Url=%s', request.get_full_path())
        if content_type == 'application/x-www-form-urlencoded':
            data = QueryDict(request.body)
            logger.info('[REQUEST] Form=%s', json.dumps(data))
        elif content_type == 'application/json' and request.body:
            data = request.body.decode('utf-8')
            logger.info('[REQUEST] Body=%s', json.loads(data))

    def process_exception(self, request, exception):
        http_status_code, code, msg = ConstCode.ServerError
        logger.exception(exception)
        if isinstance(exception, ResponseError):
            http_status_code, code, msg = exception.status_code, exception.code, exception.detail
        response = HttpResponse(content='{"code": %s, "msg": "%s"}' % (code, msg),
                                content_type='application/json',
                                status=http_status_code)
        return response
