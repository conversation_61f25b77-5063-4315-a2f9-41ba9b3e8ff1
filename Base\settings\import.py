#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from . import *

# 数智源数据同步接口
SOYUAN_SYNC_HOST = '127.0.0.1:18002'
# 数智源同步任务开启状态
SYNC_SCHEDULER_ON = False

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3'
    },
    'ljfl_auth_manager_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_auth_manager_db',
        'USER': 'auth_manager',
        'HOST': '127.0.0.1',
        'PASSWORD': 'Auth_manager20220623',
        'PORT': 33306,
        'OPTIONS': {'charset': 'utf8'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'auth_manager',
        'HOST': '127.0.0.1',
        'PASSWORD': 'Auth_manager20220623',
        'PORT': 33306,
        'OPTIONS': {'charset': 'utf8'}
    }
}
