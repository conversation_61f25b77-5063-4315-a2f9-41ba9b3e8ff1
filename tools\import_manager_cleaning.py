#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import datetime
import os

import django
import xlrd

from Base.utils import get_uuid

# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.local_testing")
django.setup()

from OAuthApp.models import Manager


def import_manager():
    excel = xlrd.open_workbook('./tools/收集设施账号信息1031.xlsx')
    sheet = excel.sheet_by_name('Sheet1')
    for i, row in enumerate(sheet.get_rows()):
        if i == 0:
            continue
        _, _, tp, uuid, name, username, password, *_ = [
            itm.value for itm in row
        ]

        check = Manager.objects.filter(username=username).first()  # type: Manager
        if check:
            print(f'username:{username} already exist.')
            check.set_password(password)
            check.save()
            continue

        city_coding = '110000000000'
        city_name = '北京市'

        if tp == "密闭式清洁站":
            role = "CleaningPointManager"
            extra_confiture = dict(cleaning_point_id=uuid)
        else:
            role = "TransferPointManager"
            extra_confiture = dict(transfer_point_id=uuid)

        manager = Manager(
            app_id=117998218,
            role=role,
            relation_id=get_uuid(),
            username=username,
            realname=name,
            city_coding=city_coding,
            city_name=city_name,
            area_coding="",
            area_name="",
            street_coding="",
            street_name="",
            comm_coding="",
            comm_name="",
            front_uri="",
            backend_uri="",
            api_uri="",
            remark="",
            expire_in=0,
            refresh_expire_in=0,
            is_declare=0,
            state=0,
            extra_configure=extra_confiture,
            add_time=datetime.datetime.now()
        )
        manager.set_password(password)
        manager.save()


if __name__ == '__main__':
    import_manager()
