import binascii
import datetime
import json
from functools import wraps

from django.conf import settings
from django.core.cache import cache

from Base.utils.soyuan_syncor import datav_syncor
from OAuthApp.models import AgencyType, Navigation, Site, AgencyTypeRoleSettings, Agency, ManagerJobRelation, Manager, \
    JobRoleSettings, Job, Department
from Base.api import get_logger
from Base.utils.encrypt import password_cryptor
from OAuthApp.scheduler.utils import handle_db_connections

logger = get_logger('scheduler')


def exception_catch(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(f'Task {func.__name__} prepare to run')
        result = None
        try:
            result = func(*args, **kwargs)
        except Exception as e:
            logger.error(f'Task {func.__name__} error:')
            logger.exception(e)
            raise e
        logger.info(f'Task {func.__name__} Done.')
        return result

    return wrapper


@exception_catch
@handle_db_connections
def sync_agency_type():
    agency_types = AgencyType.objects.exclude(soyuan_role='').filter(sync_to_soyuan=0).all()
    for at in agency_types:
        level = at.soyuan_role
        agency_ids = Agency.objects.filter(agency_type=at.type).values_list('id')

        # 系统管理员权限同步
        accounts = ManagerJobRelation.objects.filter(agency_id__in=agency_ids, is_sys=1) \
            .select_related('manager').values_list('manager__username', flat=True).distinct()
        if accounts:
            soyuan_sites = [st for st in at.available_sites.split(',') if 'SoyuanVisualization' in st]
            site_ids = Site.objects.filter(code__in=soyuan_sites).values_list('id')
            nav_ids = AgencyTypeRoleSettings.objects.filter(agency_type_id=at.id).values_list('navigation_id')
            modules = Navigation.objects.filter(site_id__in=site_ids, id__in=nav_ids).values_list(
                'component_name', flat=True
            )
            datav_syncor.sync_permission(level, list(accounts), list(modules))

        # 职务管理员权限同步 - 标识相应用户为未同步状态
        ManagerJobRelation.objects.filter(agency_id__in=agency_ids, is_sys=0).update(sync_to_soyuan=0)

        at.sync_to_soyuan = 1
        at.save()
        logger.info(f'Sync AgencyType {at.type} Done.')


@exception_catch
@handle_db_connections
def sync_job():
    job_ids = Job.objects.filter(sync_to_soyuan=0).values_list('id')
    # 职务管理员权限同步 - 标识相应用户为未同步状态
    ManagerJobRelation.objects.filter(job_id__in=job_ids).update(sync_to_soyuan=0)
    Job.objects.update(sync_to_soyuan=1)


@exception_catch
@handle_db_connections
def sync_manager():
    agency_type = AgencyType.objects.exclude(soyuan_role='').all()
    agency_type_mapping = {at.type: at.soyuan_role for at in agency_type}

    agency = Agency.objects.select_related('agency_type').all()
    agency_mapping = {ag.id: ag.agency_type.type for ag in agency}
    agency_name_mapping = {ag.id: ag.name for ag in agency}
    agency_config_mapping = {ag.id: ag.auth_configure for ag in agency}

    relations = ManagerJobRelation.objects.filter(sync_to_soyuan=0).select_related('manager').all()
    permissions = dict()
    for staff in relations:
        level = agency_type_mapping.get(agency_mapping.get(staff.agency_id))
        if not level:
            continue

        unit_name = agency_name_mapping.get(staff.agency_id)
        if staff.department_id:
            department = Department.objects.filter(pk=staff.department_id).first()
            if department:
                unit_name = department.name

        manager = staff.manager
        perm_key = (level, manager.username)
        if perm_key not in permissions:
            if not manager.encrypted_password:
                logger.info(f'Sync manager {manager.username}, but encrypted_password is empty.')
                continue

            password_hex = binascii.a2b_hex(manager.encrypted_password)
            data = dict(
                account=manager.username,
                password=password_cryptor.decrypt(password_hex),
                realname=manager.realname,
                status=manager.state,
                level=level,
                unit=unit_name
            )
            if level == 'facility':
                config = json.loads(agency_config_mapping.get(staff.agency_id))
                data.update(
                    facility_type=config.get("value", {}).get("terminal_type_id", "8d003746392711eb9f70485f99c1b734")
                )
            datav_syncor.sync_manager(**data)

            permissions[perm_key] = set()
        permissions[perm_key].add(staff.job_id)

        staff.sync_to_soyuan = 1
        staff.save()

    _agent_role_map = dict()
    _job_modules_map = dict()

    def _get_agent_role_modules(_soyuan_role):
        if _soyuan_role in _agent_role_map:
            return _agent_role_map.get(_soyuan_role, set())
        _agent_type = AgencyType.objects.filter(state=0, soyuan_role=_soyuan_role).first()
        _soyuan_sites = [st for st in _agent_type.available_sites.split(',') if 'SoyuanVisualization' in st]
        _site_ids = Site.objects.filter(code__in=_soyuan_sites).values_list('id')
        _nav_ids = AgencyTypeRoleSettings.objects.filter(agency_type_id=_agent_type.id).values_list('navigation_id')
        _modules = set(
            Navigation.objects.filter(site_id__in=_site_ids, id__in=_nav_ids).values_list('component_name', flat=True)
        )
        _agent_role_map[_soyuan_role] = _modules
        return _modules

    def _get_job_modules(_soyuan_role, _job_id):
        _key = (_soyuan_role, _job_id)
        if _key in _job_modules_map:
            return _job_modules_map.get(key, set())
        _job_role_modules = _get_agent_role_modules(_soyuan_role)
        if _job_id:
            _job_nav_ids = JobRoleSettings.objects.filter(job_id=_job_id).values_list('navigation_id')
            _job_nav_modules = set(
                Navigation.objects.filter(id__in=_job_nav_ids).values_list('component_name', flat=True)
            )
            _job_role_modules = _job_role_modules & _job_nav_modules
        _job_modules_map[key] = _job_role_modules
        return _job_role_modules

    # 同步变更的管理员权限
    for key, job_ids in permissions.items():
        soyuan_role, account = key
        modules = set()
        for jid in job_ids:
            modules = modules | _get_job_modules(soyuan_role, jid)
        datav_syncor.sync_permission(soyuan_role, [account], list(modules))



@exception_catch
@handle_db_connections
def sync_manager_recently():
    agency_type = AgencyType.objects.exclude(soyuan_role='').all()
    agency_type_mapping = {at.type: at.soyuan_role for at in agency_type}

    agency = Agency.objects.select_related('agency_type').all()
    agency_mapping = {ag.id: ag.agency_type.type for ag in agency}
    agency_name_mapping = {ag.id: ag.name for ag in agency}
    agency_config_mapping = {ag.id: ag.auth_configure for ag in agency}

    recent = cache.get("SyncRecently", datetime.datetime.now() - datetime.timedelta(minutes=10))
    relations = ManagerJobRelation.objects.select_related('manager').filter(update_time__gte=recent).all()
    permissions = dict()
    for staff in relations:
        level = agency_type_mapping.get(agency_mapping.get(staff.agency_id))
        if not level:
            continue

        unit_name = agency_name_mapping.get(staff.agency_id)
        if staff.department_id:
            department = Department.objects.filter(pk=staff.department_id).first()
            if department:
                unit_name = department.name

        manager = staff.manager
        if staff.update_time > recent:
            recent = staff.update_time
        perm_key = (level, manager.username)
        if perm_key not in permissions:
            if not manager.encrypted_password:
                logger.info(f'Sync manager {manager.username}, but encrypted_password is empty.')
                continue

            password_hex = binascii.a2b_hex(manager.encrypted_password)
            data = dict(
                account=manager.username,
                password=password_cryptor.decrypt(password_hex),
                realname=manager.realname,
                status=manager.state,
                level=level,
                unit=unit_name
            )
            if level == 'facility':
                config = json.loads(agency_config_mapping.get(staff.agency_id))
                data.update(
                    facility_type=config.get("value", {}).get("terminal_type_id", "8d003746392711eb9f70485f99c1b734")
                )
            datav_syncor.sync_manager(**data)

            permissions[perm_key] = set()
        permissions[perm_key].add(staff.job_id)

    cache.set("SyncRecently", recent)

    _agent_role_map = dict()
    _job_modules_map = dict()

    def _get_agent_role_modules(_soyuan_role):
        if _soyuan_role in _agent_role_map:
            return _agent_role_map.get(_soyuan_role, set())
        _agent_type = AgencyType.objects.filter(state=0, soyuan_role=_soyuan_role).first()
        _soyuan_sites = [st for st in _agent_type.available_sites.split(',') if 'SoyuanVisualization' in st]
        _site_ids = Site.objects.filter(code__in=_soyuan_sites).values_list('id')
        _nav_ids = AgencyTypeRoleSettings.objects.filter(agency_type_id=_agent_type.id).values_list('navigation_id')
        _modules = set(
            Navigation.objects.filter(site_id__in=_site_ids, id__in=_nav_ids).values_list('component_name', flat=True)
        )
        _agent_role_map[_soyuan_role] = _modules
        return _modules

    def _get_job_modules(_soyuan_role, _job_id):
        _key = (_soyuan_role, _job_id)
        if _key in _job_modules_map:
            return _job_modules_map.get(key, set())
        _job_role_modules = _get_agent_role_modules(_soyuan_role)
        if _job_id:
            _job_nav_ids = JobRoleSettings.objects.filter(job_id=_job_id).values_list('navigation_id')
            _job_nav_modules = set(
                Navigation.objects.filter(id__in=_job_nav_ids).values_list('component_name', flat=True)
            )
            _job_role_modules = _job_role_modules & _job_nav_modules
        _job_modules_map[key] = _job_role_modules
        return _job_role_modules

    # 同步变更的管理员权限
    for key, job_ids in permissions.items():
        soyuan_role, account = key
        modules = set()
        for jid in job_ids:
            modules = modules | _get_job_modules(soyuan_role, jid)
        datav_syncor.sync_permission(soyuan_role, [account], list(modules))


def sync_mixin():
    if settings.SYNC_SCHEDULER_RECENTLY:
        sync_manager_recently()
    else:
        sync_agency_type()
        sync_job()
        sync_manager()
