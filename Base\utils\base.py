#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import time
import uuid


def get_current_timestamp():
    """
    获取当前时间戳 秒级别
    :return:
    """
    return int(time.time())


def get_uuid_str():
    """
    获取字符类型uuid,不含-
    :return:
    """
    return str(uuid.uuid1()).replace('-', '')


def auto_instance_load(instance, data):
    """
    自动加载model
    :param instance:
    :param data:
    :return:
    """
    for k, v in data.items():
        if hasattr(instance, k):
            setattr(instance, k, v)
