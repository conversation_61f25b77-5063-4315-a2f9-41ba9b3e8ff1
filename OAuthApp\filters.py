from django_filters import FilterSet, Cha<PERSON><PERSON><PERSON><PERSON>, NumberFilter

from OAuthApp.models import Site, Navigation, ManagerJobRelation, AgencyType, AgencyTypeRoleSettings, Job


class NavigationFilter(FilterSet):
    site_code = CharFilter(method='filter_site_code')

    @staticmethod
    def filter_site_code(queryset, name, value):
        site = Site.objects.filter(code=value).first()
        queryset = queryset.filter(site_id=site.id if site else -1)
        return queryset

    class Meta:
        model = Navigation
        fields = ('site_id', 'parent_id', 'state')


class JobStaffFilter(FilterSet):
    exclude_job_id = NumberFilter(method='filter_exclude_job_id')

    @staticmethod
    def filter_exclude_job_id(queryset, name, value):
        exclude_manager_ids = ManagerJobRelation.objects.filter(job_id=value).values_list('manager_id')
        queryset = queryset.exclude(manager_id__in=exclude_manager_ids)
        return queryset

    class Meta:
        model = ManagerJobRelation
        fields = ('agency_id', 'department_id', 'job_id')


class AgencyTypeRoleSettingsFilter(FilterSet):
    agency_type_code = NumberFilter(method='filter_agency_type_code')

    @staticmethod
    def filter_agency_type_code(queryset, name, value):
        agency_type = AgencyType.objects.filter(type=value).first()
        queryset = queryset.filter(agency_type_id=agency_type.id if agency_type else 0)
        return queryset

    class Meta:
        model = AgencyTypeRoleSettings
        fields = ('agency_type_id',)


class JobFilter(FilterSet):
    agency_id = NumberFilter(method='filter_agency_id')
    department_id = NumberFilter(method='filter_department_id')

    @staticmethod
    def filter_agency_id(queryset, name, value):
        queryset = queryset.filter(agency_id=value)
        return queryset

    @staticmethod
    def filter_department_id(queryset, name, value):
        queryset = queryset.filter(department_id=value)
        return queryset

    class Meta:
        model = Job
        fields = ('state',)
