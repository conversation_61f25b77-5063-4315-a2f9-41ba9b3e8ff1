from django.db import connections


def close_old_connections():
    """
    关闭不可用mysql连接
    :return:
    """
    for conn in connections.all():
        conn.close_if_unusable_or_obsolete()


def handle_db_connections(func):
    """
    自动管理Mysql连接
    :param func:
    :return:
    """

    def func_wrapper(*args, **kwargs):
        close_old_connections()
        result = func(*args, **kwargs)
        close_old_connections()
        return result

    return func_wrapper
