import atexit

from apscheduler.schedulers.background import BackgroundScheduler

from Base.api import get_logger
from OAuthApp.scheduler.task_sync_to_soyuan import sync_mixin

logger = get_logger('django')
scheduler = BackgroundScheduler()
scheduler.add_job(sync_mixin, 'cron', minute='*/1')

locker = open('scheduler.lock', 'wb')


def scheduler_init():
    import fcntl
    try:
        fcntl.flock(locker, fcntl.LOCK_EX | fcntl.LOCK_NB)
        scheduler.start()
    except Exception as e:
        print(e)

    def unlock():
        fcntl.flock(locker, fcntl.LOCK_UN)
        locker.close()

    atexit.register(unlock)
