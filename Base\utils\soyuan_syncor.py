import json

import requests
from django.conf import settings

from Base.api import get_logger

logger = get_logger('django')


class SoyuanManager:

    def __init__(self, account, password, realname, level, status=0, unit="", facility_type=None):
        self.account = account
        self.password = password
        self.realname = realname
        self.level = level
        self.status = status
        self.unit = unit
        self.facility_type = facility_type

    def to_dict(self):
        return dict(
            account=self.account,
            password=self.password,
            realname=self.realname,
            status=self.status,
            level=self.level,
            unit=self.unit,
            facility_type=self.facility_type
        )


class SoyuanSyncor:

    def __init__(self):
        self.host = settings.SOYUAN_SYNC_HOST

    def _post(self, url, data):
        if not self.host:
            return

        try:
            response = requests.post(url, json=data)
            result = json.loads(response.content)
            return result
        except Exception as e:
            logger.error('Error on sync to soyuan:')
            logger.exception(e)
            return dict(code=1, result='False', msg=str(e))

    def sync_manager(self, account, password, realname, level, status, unit, facility_type=None):
        url = f'http://{self.host}/ztbory/api/external/syncUser'
        data = dict(
            account=account,
            password=password,
            realName=realname,
            status=status,
            level=level,
            unit=unit,
        )
        if facility_type:
            data.update(facilityType=facility_type)
        return self._post(url, data)

    def sync_permission(self, level='', account=[], module=[]):
        url = f'http://{self.host}/ztbory/api/external/syncUserAuth'
        return self._post(url, dict(
            level=level,
            account=account,
            module=module
        ))


class DataVSyncor:

    def __init__(self):
        self.host = settings.DATAV_SYNC_HOST

    def _post(self, url, data):
        if not self.host:
            return
        try:
            response = requests.post(url, json=data)
            if response.status_code == 204:
                return
            result = json.loads(response.content)
            return result
        except Exception as e:
            logger.error('Error on sync to soyuan:')
            logger.exception(e)
            return dict(code=1, result='False', msg=str(e))

    def sync_manager(self, account, password, realname, level, status, unit, facility_type=None):
        url = f'{self.host}/interface/manager'
        data = dict(
            username=account,
            password=password,
            realname=realname,
            state=False if status else True,
            role=level,
            unit=unit,
            extra_config=dict()
        )
        if facility_type:
            data.update(extra_config=dict(facility_type=facility_type))
        return self._post(url, data)

    def sync_permission(self, level='', account=[], module=[]):
        url = f'{self.host}/interface/manager/batch'
        return self._post(url, dict(
            role=level,
            accounts=account,
            authorized=module
        ))


soyuan_syncor = SoyuanSyncor()
datav_syncor = DataVSyncor()
