#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import rest_framework.fields
from django.utils.translation import gettext_lazy as _

import datetime
import decimal
import json  # noqa
import uuid
from django.db.models.query import QuerySet
from django.utils import timezone
from django.utils.encoding import force_str
from django.utils.functional import Promise
from rest_framework import serializers
from rest_framework.compat import coreapi
from rest_framework.utils import encoders
from rest_framework import status

rest_framework.fields.CharField.default_error_messages = {
    'invalid': _('不是有效的字符.'),
    'blank': _('不可为空.'),
    'max_length': _('不可超过{max_length}个字符.'),
    'min_length': _('不可少于{min_length}个字符.'),
}

rest_framework.fields.ChoiceField.default_error_messages = {
    'invalid_choice': _('"{input}" 不是一个有效的选项.')
}

rest_framework.fields.DecimalField.default_error_messages = {
    'invalid': _('需要提供有效的数字.'),
    'max_value': _('数值应小于或等于{max_value}.'),
    'min_value': _('数值应大于或等于{min_value}.'),
    'max_digits': _('最多保留{max_digits}位小数.'),
    'max_decimal_places': _('Ensure that there are no more than {max_decimal_places} decimal places.'),
    'max_whole_digits': _('Ensure that there are no more than {max_whole_digits} digits before the decimal point.'),
    'max_string_length': _('数值过大.')
}


class JSONEncoder(json.JSONEncoder):
    """
    JSONEncoder subclass that knows how to encode date/time/timedelta,
    decimal types, generators and other basic python objects.
    """

    def default(self, obj):
        # For Date Time string spec, see ECMA 262
        # https://ecma-international.org/ecma-262/5.1/#sec-15.9.1.15
        if isinstance(obj, Promise):
            return force_str(obj)
        elif isinstance(obj, datetime.datetime):
            # representation = obj.isoformat()
            # if representation.endswith('+00:00'):
            #     representation = representation[:-6] + 'Z'
            # return representation
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, datetime.date):
            # return obj.isoformat()
            return obj.strftime('%Y-%m-%d')
        elif isinstance(obj, datetime.time):
            if timezone and timezone.is_aware(obj):
                raise ValueError("JSON can't represent timezone-aware times.")
            representation = obj.isoformat()
            return representation
        elif isinstance(obj, datetime.timedelta):
            return str(obj.total_seconds())
        elif isinstance(obj, decimal.Decimal):
            # Serializers will coerce decimals to strings by default.
            return float(obj)
        elif isinstance(obj, uuid.UUID):
            return str(obj)
        elif isinstance(obj, QuerySet):
            return tuple(obj)
        elif isinstance(obj, bytes):
            # Best-effort for binary blobs. See #4187.
            return obj.decode()
        elif hasattr(obj, 'tolist'):
            # Numpy arrays and array scalars.
            return obj.tolist()
        elif (coreapi is not None) and isinstance(obj, (coreapi.Document, coreapi.Error)):
            raise RuntimeError(
                'Cannot return a coreapi object from a JSON view. '
                'You should be using a schema renderer instead for this view.'
            )
        elif hasattr(obj, '__getitem__'):
            cls = (list if isinstance(obj, (list, tuple)) else dict)
            try:
                return cls(obj)
            except Exception:
                pass
        elif hasattr(obj, '__iter__'):
            return tuple(item for item in obj)
        return super().default(obj)


encoders.JSONEncoder = JSONEncoder


class JsonSerializerField(serializers.JSONField):
    default_error_messages = {
        'invalid_json': _('无效的json数据格式')
    }

    def to_representation(self, value):
        return json.loads(value)

    def to_internal_value(self, data):
        try:
            json.loads(data)
        except (TypeError, ValueError):
            self.fail('invalid_json')
        return data


class ResponseError(Exception):

    def __init__(self, const_code, detail, status_code=None):
        http_status_code, code, msg = const_code
        if status_code:
            http_status_code = status_code
        self.status_code = 200 if http_status_code == 400 else http_status_code
        self.code = code
        self.detail = detail or msg


status.HTTP_201_CREATED = 200
status.HTTP_204_NO_CONTENT = 200
