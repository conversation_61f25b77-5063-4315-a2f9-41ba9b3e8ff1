#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

import io
import xlwt
import decimal
import datetime
from django.db import models
from itertools import chain

from django.http import HttpResponse
from django.utils.encoding import escape_uri_path
from rest_framework import viewsets, serializers, generics, permissions
from rest_framework.response import Response
from Base.api import ApiPageNumberPagination
from Base.utils import get_params_day


def get_model_serializer(model_class):
    """
    获取模型序列化器
    :param model_class:
    :return:
    """

    class ModelSerializer(serializers.ModelSerializer):
        class Meta:
            model = model_class
            fields = '__all__'

    return ModelSerializer


def get_view_set(model_class, searches=(), filters=[]):
    """
    获取视图
    :param model_class: 表实体类
    :param searches: 模糊匹配字段
    :param filters: 过滤字段
    :return:
    """

    if not searches and hasattr(model_class, '__search_fields__'):
        searches = getattr(model_class, '__search_fields__')
    if not filters and hasattr(model_class, '__filter_fields__'):
        filters = getattr(model_class, '__filter_fields__')

    if 'residential' in filters:
        filters.remove('residential')
        searches.append('residential')

    class BaseViewSet(viewsets.ModelViewSet):
        permission_classes = (permissions.IsAuthenticated,)
        queryset = model_class.objects.all()
        serializer_class = get_model_serializer(model_class)
        pagination_class = ApiPageNumberPagination
        search_fields = searches
        filter_fields = filters

        def get_queryset(self):
            # 时间检索
            condition = dict()
            for f in model_class._meta.get_fields():
                if isinstance(f, models.DateTimeField):
                    time_start = get_params_day(self.request.query_params.get(f'start_{f.name}'))
                    time_end = get_params_day(self.request.query_params.get(f'end_{f.name}'), offset=1)
                    if time_start:
                        condition[f'{f.name}__gte'] = time_start
                    if time_end:
                        condition[f'{f.name}__lt'] = time_end
            if condition:
                self.queryset = self.queryset.filter(**condition)

            # 模糊检索
            search_condition = dict()
            for field in self.search_fields:
                value = self.request.query_params.get(field)
                if value:
                    search_condition[f'{field}__contains'] = value
            if search_condition:
                self.queryset = self.queryset.filter(**search_condition)

            # 精确检索
            filter_condition = dict()
            for field in self.filter_fields:
                # 如果登陆账号为街道，则设置默认检索街道
                if field == 'street' and self.request.user.street:
                    value = self.request.user.street
                else:
                    value = self.request.query_params.get(field)
                if value:
                    filter_condition[f'{field}'] = value
            if filter_condition:
                self.queryset = self.queryset.filter(**filter_condition)
            return self.queryset

        def list(self, request, *args, **kwargs):
            queryset = self.filter_queryset(self.get_queryset())
            if self.request.query_params.get('export') == '1':
                return self.export(queryset)

            if self.request.query_params.get('schema') == '1':
                return self.schema()

            return super().list(request, *args, **kwargs)

        def create(self, request, *args, **kwargs):
            if 'street' in self.filter_fields and self.request.user.street:
                request.data['street'] = self.request.user.street
            return super().create(request, *args, **kwargs)

        def schema(self):
            title = model_class._meta.verbose_name
            header = []
            for field in model_class._meta.get_fields():
                if field.name == 'id':
                    continue
                tp = str(type(field)).replace("<class 'django.db.models.fields.", '').replace("Field'>", '')
                _s = getattr(model_class, '__search_fields__') if hasattr(model_class, '__search_fields__') else []
                _f = getattr(model_class, '__filter_fields__') if hasattr(model_class, '__filter_fields__') else []
                is_search = 1 if field.name in _s else 0
                is_filter = 1 if field.name in _f else 0
                header.append(dict(
                    name=field.verbose_name,
                    field=field.name,
                    type=tp,
                    is_filter=is_filter,
                    is_search=is_search,
                ))
            return Response(dict(
                title=title,
                schema=header
            ))

        def export(self, queryset):
            title = model_class._meta.verbose_name
            header = []
            for field in model_class._meta.get_fields():
                header.append(field.verbose_name)
            data = queryset.values_list()

            excel = xlwt.Workbook(encoding='utf-8')
            sheet = excel.add_sheet(title)
            for i, row in enumerate(chain([header], data)):
                for j, col in enumerate(row):
                    value = col
                    if value == 'ID':
                        value = '序号'
                    elif isinstance(value, decimal.Decimal):
                        value = f'{value:.2f}'
                    elif isinstance(value, datetime.datetime):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    sheet.write(i, j, value)

            output = io.BytesIO()
            excel.save(output)
            response = HttpResponse(content_type='application/vnd.ms-excel')
            filename = f'{escape_uri_path(title)}-{datetime.datetime.now():%Y%m%d%H%M%S}.xls'
            response['Content-Disposition'] = f'attachment;filename={filename}'
            response.write(output.getvalue())
            return response

    return BaseViewSet


def get_dropdown_view_set(model_class, searches=(), filters=[]):
    """
    获取下拉菜单视图
    :param model_class: 表实体类
    :param searches: 模糊匹配字段
    :param filters: 过滤字段
    :return:
    """

    if not searches and hasattr(model_class, '__search_fields__'):
        searches = getattr(model_class, '__search_fields__')
    if not filters and hasattr(model_class, '__filter_fields__'):
        filters = getattr(model_class, '__filter_fields__')

    if 'residential' in filters:
        filters.remove('residential')
        searches.append('residential')

    class BaseViewSet(generics.ListAPIView):
        permission_classes = (permissions.IsAuthenticated,)
        queryset = model_class.objects.all()
        serializer_class = get_model_serializer(model_class)
        pagination_class = ApiPageNumberPagination
        search_fields = searches
        filter_fields = filters

        def get_queryset(self):
            # 模糊检索
            search_condition = dict()
            for field in self.search_fields:
                value = self.request.query_params.get(field)
                if value:
                    search_condition[f'{field}__contains'] = value
            if search_condition:
                self.queryset = self.queryset.filter(**search_condition)

            # 精确检索
            filter_condition = dict()
            for field in self.filter_fields:
                value = self.request.query_params.get(field)
                if value:
                    filter_condition[f'{field}'] = value
            if filter_condition:
                self.queryset = self.queryset.filter(**filter_condition)
            return self.queryset

        def list(self, request, *args, **kwargs):
            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(queryset.all(), many=True)
            return Response(serializer.data)

    return BaseViewSet
