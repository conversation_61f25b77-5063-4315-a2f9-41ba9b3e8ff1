"""
WSGI config for Base project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/howto/deployment/wsgi/
"""

import os
import socket

from django.core.wsgi import get_wsgi_application

environment = 'production'
hostname = socket.gethostname()
if hostname in ['Server-61198fa2-11ab-4fd6-8c45-cd138a1534fe.novalocal']:
    environment = 'testing'

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Base.settings.{}'.format(environment))

application = get_wsgi_application()
