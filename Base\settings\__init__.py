# !/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

"""
Django settings for Base project.

Generated by 'django-admin startproject' using Django 2.1.14.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.1/ref/settings/
"""

import os
from .. import plugin

APPLICATION_NAME = "bj_auth_manager"
ENVIRONMENT = 'development'

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), './../../'))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '*p0t3vzf-pad+7imw)cueezvag5nwswz7(#399%04cd#3k2&of'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

# 邮件通知
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_USE_TLS = False  # 是否使用TLS安全传输协议
EMAIL_USE_SSL = True  # 是否使用SSL加密
EMAIL_HOST = 'smtp.exmail.qq.com'
EMAIL_PORT = 465
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'c7HR9LF48ciebfJG'

# 数智源数据同步接口
SOYUAN_SYNC_HOST = '*************:48002'
# 数智源同步任务开启状态
SYNC_SCHEDULER_ON = False
SYNC_SCHEDULER_RECENTLY = False

# 密码错误防护
# 最多错误次数
PASSWORD_INCORRECT_TIMES = 6
# 达到次数禁用时长 s
PASSWORD_INCORRECT_DISABLE_DURATION = 10 * 60
PASSWORD_INCORRECT_TIPS = "10分钟"

# Application definition

INSTALLED_APPS = [
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.staticfiles',

    'rest_framework',
    'gunicorn',

    # apps
    'OAuthApp',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'Base.api.ApiMiddleWare',
]

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

REST_FRAMEWORK = {
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ],
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    # 过滤器
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        'Base.api.ApiJsonRender',
    ),
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S'
}

# URL禁止自动补充/
APPEND_SLASH = False

# 跨域支持
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = (
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)
CORS_ALLOW_HEADERS = (
    'XMLHttpRequest',
    'X_FILENAME',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'Pragma',
)

ROOT_URLCONF = 'Base.urls'

WSGI_APPLICATION = 'Base.wsgi.application'

# Database
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases

# Mysql Connection
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3'
    },
    'ljfl_auth_manager_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_auth_manager_db',
        'USER': 'root',
        'HOST': '************',
        'PASSWORD': 'zhitongbr@2020',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'root',
        'HOST': '************',
        'PASSWORD': 'zhitongbr@2020',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8'}
    }
}

DATABASE_ROUTERS = ['Base.db_router.DatabaseAppsRouter']

DATABASE_APPS_MAPPING = {
    'ljfl_auth_manager_db': 'ljfl_auth_manager_db',
    'ljfl_db': 'ljfl_db',
}

SOUTH_TESTS_MIGRATE = False
# logging settings
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {  # 格式化
        'simple': {
            'format': '%(asctime)s %(levelname)6s - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'console': {
            'format': '%(asctime)s %(levelname)8s - %(message)s',
            # 'format': '[%(asctime)s][%(levelname)s] %(pathname)s %(lineno)d -> %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'handlers': {  # 处理器
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'console'
        },
        'fileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'log.log'),
            'when': 'midnight',
            'encoding': 'utf8',
            'backupCount': 180,
        },
        'schedulerHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'scheduler.log'),
            'maxBytes': 1024 * 1024 * 100,
            'encoding': 'utf8',
            'backupCount': 5,
        }
    },
    'loggers': {  # 记录器
        'django': {
            'handlers': ['fileHandler', 'console'],
            'level': 'INFO',
            'propagate': False
        },
        'scheduler': {
            'handlers': ['schedulerHandler'],
            'level': 'INFO',
            'propagate': False
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'filter': ['require_debug_true'],
            'propagate': False,
        },
    }
}

# redis配置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://172.16.1.3:6379/4",
        'KEY_PREFIX': 'api:ljflAuthManager',
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
        }
    }
}

# Password validation
# https://docs.djangoproject.com/en/2.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/2.1/topics/i18n/

LANGUAGE_CODE = 'zh-Hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')
