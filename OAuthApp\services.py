#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import datetime
import json
import re
from urllib.parse import unquote

from django.conf import settings
from django.contrib.auth.hashers import check_password
from django.core.cache import cache
from django.forms.models import model_to_dict
from gmssl import sm2

from Base.api import ApiResponse, ConstCode, ConstRole, get_logger
from Base.plugin import ResponseError
from Base.utils import get_uuid, get_coding, get_random_secret, get_ip
from Base.utils.coding import get_coding_name
from Lic.core import token_required, token_required_or_implicit, proxy_request, gen_jwt_token, gen_jwt_refresh_token
from .models import Application, Manager, Role, OperationLog, ManagerJobRelation, Agency, CityOperationLog, Department, \
    CityOperationOnLog
from .models_base import CityRegion, FactoryLocation
from .serializers import ManagerRegisterSerializer, ManagerRepasswordSerializer, ManagerLogoffSerializer, \
    ManagerResetPasswordSerializer, ManagerFakeRegionSerializer, ManagerResetPasswordSimpleSerializer, \
    ManagerResetUsernameSimpleSerializer, ManagerChangeMyPasswordSerializer

logger = get_logger('django')


def get_roles():
    roles = Role.objects.filter(is_deleted=0).values_list('role', flat=True)
    return roles


def get_roles_dict():
    role_dict = {i.role: i.role for i in Role.objects.filter(is_deleted=0)}
    return role_dict


role_obj = get_roles_dict()


def get_manager_response_dict(manager, param_version):
    manager_response = model_to_dict(manager,
                                     exclude=['id', 'app_id', 'password', 'encrypted_password', 'salt',
                                              'front_uri', 'backend_uri', 'api_uri',
                                              'remark', 'state', 'expire_in', 'refresh_expire_in'])
    # 新平台登陆限制
    relation = ManagerJobRelation.objects.filter(manager_id=manager.id).first()
    if param_version == 'V2.0' and not relation:
        raise ResponseError(const_code=ConstCode.BadRequest, detail='账户不存在或密码错误,如有疑问请联系管理员.')

    if relation:
        agency = Agency.objects.filter(id=relation.agency_id).first()
        if param_version == 'V2.0' and (not agency or agency.state):
            raise ResponseError(const_code=ConstCode.BadRequest, detail='机构已被禁用,如有疑问请联系管理员.')

        if agency:
            configure = json.loads(agency.auth_configure)
            configure_type = configure.get('type', 'REGION')
            configure_value = configure.get('value')
            if configure_type == 'TERMINAL':
                terminal_id = configure_value.get('terminal_factory_id')
                configure_value = {}
                if terminal_id:
                    terminal = FactoryLocation.objects.filter(factory_location_id=terminal_id).first()
                    configure_value = dict(
                        terminal_type_id=terminal.factory_type,
                        terminal_type_name=terminal.type,
                        terminal_factory_id=terminal.factory_location_id,
                        terminal_factory_name=terminal.alias_name or terminal.name
                    )
            # 默认 REGION
            else:
                for key in list(configure_value.keys()):
                    name_key = key.replace('coding', 'name')
                    if hasattr(manager, name_key):
                        configure_value[name_key] = getattr(manager, name_key)
            manager_response['agency_configure'] = configure_value

            department = Department.objects.filter(id=relation.department_id).first()
            manager_response['agency_department'] = dict(
                agency_id=agency.id,
                agency_name=agency.name,
                department_id=department.id if department else 0,
                department_name=department.name if department else '系统'
            )
    return manager_response


def complex_regex(password: str):
    complex_min_length: int = 8
    complex_max_length: int = 32
    regex = "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d!\\\"#$%&'()*+,-./:;<=>?@\\\\\\]\\[^_`{|}~]"
    """完整的密码校验正则表达式"""
    return re.match(r"^%s{%d,%d}$" % (regex, complex_min_length, complex_max_length), password)


def apply_token(request):
    params = request.query_params.dict()
    grant_type = params.get('grant_type')
    if grant_type not in ('password', 'implicit'):
        return ApiResponse(code=ConstCode.BadRequest, msg='无效的授权方式.')

    appid = params.get('appid')
    appsecret = params.get('appsecret')
    if not appid:
        return ApiResponse(code=ConstCode.BadRequest, msg='无效的AppId或AppSecret.')
    application = Application.objects.filter(app_id=appid)
    if grant_type == 'implicit':
        application = application.filter(app_secret=appsecret)
    application = application.first()

    if not application:
        return ApiResponse(code=ConstCode.BadRequest, msg='无效的AppId或AppSecret.')

    if application.state:
        return ApiResponse(code=ConstCode.BadRequest, msg='应用已禁用,如有疑问请联系管理员.')

    condition = dict()

    username = params.get('username')
    password = unquote(params.get('password'))
    param_ip = params.get('ip')
    param_version = params.get('v')
    param_source = params.get('s', 'PC')
    if grant_type == 'implicit':
        password = '0|0|0|0|0|0'
    elif not complex_regex(password):
        return ApiResponse(code=ConstCode.BadRequest, msg='密码强度较弱，请联系管理员修改')

    # # 超过90天强制更改密码
    # if (datetime.datetime.now() - manager.last_password_time).days > 90:
    #     return ApiResponse(code=ConstCode.BadRequest, msg='您的密码已超过90天未变更，请修改密码.')

    # # 密码复杂度判断
    # # 1. 8~64位长度判断
    # if len(password) < 8 or len(password) > 64:
    #     return ApiResponse(code=ConstCode.BadRequest, msg='8-32位字符，要求同时具备英文大写、小写、阿拉伯数字')
    # # 2. 必须包含大写字母、小写字母、数字（特殊字符暂不要求）
    # if re.match('^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])).*$', password) == None:
    #     return ApiResponse(code=ConstCode.BadRequest, msg='8-32位字符，要求同时具备英文大写、小写、阿拉伯数字')
    # # 3. 密码不能包含用户名，包含大小写
    # if username.upper() in password.upper():
    #     return ApiResponse(code=ConstCode.BadRequest, msg='密码中不应包含用户名.')

    if not all([appid, username, password]):
        return ApiResponse(code=ConstCode.BadRequest, msg='请提供管理员凭证信息.')

    # 账号登录限制
    manager_key = 'Manager:{}'.format(username)
    manager_error_num = cache.get(manager_key, 0)
    if manager_error_num == 6:
        return ApiResponse(code=ConstCode.BadRequest, msg='账号已被锁定.')

    condition['app_id'] = application.app_id
    condition['username'] = username
    condition['password'] = password

    password = condition.pop('password', None)
    manager = Manager.objects.filter(**condition).first()

    # 获取用户ip
    if not param_ip:
        ip = get_ip(request)
    else:
        ip = param_ip
    if grant_type == 'password':
        if not password:
            return ApiResponse(code=ConstCode.BadRequest, msg='账户不存在或密码错误.')
        password = password.strip()
        if password and (manager is None or not check_password(password=password, encoded=manager.password)):
            if not (settings.ENVIRONMENT == 'testing' and password == 'Zhitongtest0'):
                manager_error_num += 1
                if manager_error_num == settings.PASSWORD_INCORRECT_TIMES:
                    cache.set(manager_key, manager_error_num, settings.PASSWORD_INCORRECT_DISABLE_DURATION)
                else:
                    cache.set(manager_key, manager_error_num)

                if manager:
                    create_login_log(ip, manager.relation_id, manager.username, '账户不存在或密码错误')
                return ApiResponse(code=ConstCode.BadRequest, msg=f"账户不存在或密码错误.密码错误{settings.PASSWORD_INCORRECT_TIMES}次账户将会锁定{settings.PASSWORD_INCORRECT_TIPS}, 目前错误次数:{manager_error_num}.")

    if not manager:
        return ApiResponse(code=ConstCode.BadRequest, msg='账户不存在或密码错误.')

    if manager.state:
        return ApiResponse(code=ConstCode.BadRequest, msg='账户已禁用,如有疑问请联系管理员.')

    expire_in = manager.expire_in or application.expire_in
    token = gen_jwt_token(manager, expire_in)
    login_id = get_uuid()
    manager_response = get_manager_response_dict(manager, param_version)
    if param_version == 'V2.0':
        manager_response["login_id"] = login_id

    token_value = dict(appid=application.app_id, manager=manager_response)
    refresh_expire_in = manager.refresh_expire_in or application.refresh_expire_in
    cache.set('Token:{}'.format(token), token_value, expire_in)

    refresh_token = gen_jwt_refresh_token(manager, refresh_expire_in)
    cache.set('RefreshToken:{}'.format(refresh_token),
              dict(token=token, value=token_value, expire_in=expire_in),
              refresh_expire_in)

    # 登录成功且小于限制次数 则清除限制
    if manager_error_num:
        cache.delete(manager_key)

    create_login_log(ip, manager.relation_id, manager.username, '登录成功',
                     login_id=login_id, version=param_version, source=param_source)
    return ApiResponse(code=ConstCode.Success,
                       data=dict(token=token,
                                 expire_in=expire_in,
                                 refresh_token=refresh_token))


def create_login_log(ip, relation_id, username, content, login_id=None, login_type=10, version='', source='PC'):
    # 登录日志
    doc = {
        'ip': ip,
        'relation_id': relation_id,
        'username': username,
        'content': content,
        'login_type': login_type,
    }
    OperationLog.objects.create(**doc)
    if version:
        operation = dict(
            operation_type='LOGIN',
            operation_source=source,
            username=username,
            relation_id=relation_id,
            ip=ip,
            content=content,
            version=version,
        )
        log = CityOperationLog.objects.create(**operation)

        if login_id:
            operation.update(login_id=login_id, login_time=log.create_time)
            CityOperationOnLog.objects.create(**operation)


def update_logout_log(login_id):
    CityOperationOnLog.objects.filter(login_id=login_id).update(logout_time=datetime.datetime.now())


def _query_manager_by_appid_token(appid, token):
    token_value = cache.get('Token:{}'.format(token))
    if not token_value or str(token_value.get('appid', '')) != appid:
        return None
    return token_value


def do_refresh_token(request):
    params = request.query_params.dict()
    appid = params.get('appid')
    refresh_token = params.get('refresh_token')
    if not all([appid, refresh_token]):
        return ApiResponse(code=ConstCode.BadRequest, msg='请提供AppId以及RefreshToken.')

    refresh_token_value = cache.get('RefreshToken:{}'.format(refresh_token))
    if not refresh_token_value:
        return ApiResponse(code=ConstCode.UnAuthorized, msg='RefreshToken无效或已过期.')

    token = refresh_token_value.get('token')
    token_value = refresh_token_value.get('value')
    expire_in = refresh_token_value.get('expire_in')
    if not token or not token_value or str(token_value.get('appid', '')) != appid:
        return ApiResponse(code=ConstCode.UnAuthorized, msg='RefreshToken无效或已过期.')

    cache.expire('Token:{}'.format(token), expire_in)
    return ApiResponse(code=ConstCode.Success,
                       data=dict(token=token,
                                 expire_in=expire_in))


@token_required
def manager_by_token(request):
    manager = get_manager_response_dict(request.token_manager, None)

    # 内部可切换区域管理员
    if manager.get('role') == role_obj.get('FakeManager'):
        role = role_obj.get('CityManager')
        if manager.get('area_coding'):
            role = role_obj.get('AreaManager')
        if manager.get('street_coding'):
            role = role_obj.get('StreetManager')
        if manager.get('comm_coding'):
            role = role_obj.get('CommManager')
        manager['role'] = role
        manager['is_fake'] = True

    return ApiResponse(code=ConstCode.Success,
                       data=dict(manager=manager,
                                 token_expire=request.token_expire))


@token_required_or_implicit
def manager_register(request):
    serializer_model = ManagerRegisterSerializer(data=request.data)
    if not serializer_model.is_valid():
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='提交数据有误!',
                           errors=serializer_model.errors)
    role = serializer_model.data.get('role')
    if role not in get_roles():
        return ApiResponse(code=ConstCode.BadRequest, msg='无效的角色!')

    check = Manager.objects.filter(username=serializer_model.data.get('username')).first()
    if check:
        return ApiResponse(code=ConstCode.BadRequest, msg='用户名已存在!')

    check = Manager.objects.filter(relation_id=serializer_model.data.get('relation_id')).first()
    if check:
        return ApiResponse(code=ConstCode.BadRequest, msg='关联id已存在!')

    data = dict(serializer_model.data)

    if request.token_type == 'token':
        manager = request.token_manager
        city_coding = '110000000000'
        area_coding = manager.area_coding
        street_coding = manager.street_coding
        comm_coding = manager.comm_coding
    else:
        city_coding = '110000000000'
        area_coding = ''
        street_coding = ''
        comm_coding = ''

    coding = data.pop('coding', '')
    if coding:
        p_area_coding = get_coding(coding=coding, grade=2)
        p_street_coding = get_coding(coding=coding, grade=3)
        p_comm_coding = get_coding(coding=coding, grade=4)
        if any([
            area_coding and p_area_coding != area_coding,
            street_coding and p_street_coding != street_coding,
            comm_coding and p_comm_coding != comm_coding,
        ]):
            return ApiResponse(code=ConstCode.BadRequest, msg='无权创建其他区域管理员!')
        area_coding = p_area_coding or area_coding
        street_coding = p_street_coding or street_coding
        comm_coding = p_comm_coding or comm_coding

    # 入户宣传人员/非居民责任主体，Token7日失效
    if role in [role_obj.get('Publicists'), role_obj.get('SocialOrganization')]:
        data.update(dict(expire_in=7 * 24 * 60 * 60, refresh_expire_in=1))

    # 用户名去除空格
    data["username"] = data["username"].replace(" ", "")

    manager_data = dict(
        app_id=request.appid,
        salt=get_random_secret(32),
        city_coding=city_coding,
        city_name=get_coding_name(city_coding),
        area_coding=area_coding,
        area_name=get_coding_name(area_coding),
        street_coding=street_coding,
        street_name=get_coding_name(street_coding),
        comm_coding=comm_coding,
        comm_name=get_coding_name(comm_coding),
        add_time=datetime.datetime.now()
    )
    password = data.pop('password', None)
    manager_data.update(**data)
    manager_for_save = Manager(**manager_data)
    manager_for_save.set_password(password)
    manager_for_save.save()
    manager_response = model_to_dict(manager_for_save,
                                     exclude=['id', 'app_id', 'password', 'front_uri', 'backend_uri', 'api_uri',
                                              'remark', 'state', 'expire_in', 'refresh_expire_in'])
    return ApiResponse(code=ConstCode.Success,
                       data=manager_response)


@token_required
def manager_fake_region(request):
    serializer_model = ManagerFakeRegionSerializer(data=request.data)
    if not serializer_model.is_valid():
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='提交数据有误!',
                           errors=serializer_model.errors)

    coding = serializer_model.data.get('coding')
    region = CityRegion.objects.filter(coding=coding, is_deleted=0).first()
    if not region:
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='无效的行政区域编码!')

    area_coding = get_coding(coding, 2)
    street_coding = get_coding(coding, 3)
    comm_coding = get_coding(coding, 4)

    appid = request.appid
    token = request.token
    manager = request.token_manager
    if manager.role != ConstRole.FakeManager:
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='无效账户!')
    manager_response = model_to_dict(manager,
                                     exclude=['id', 'app_id', 'password', 'front_uri', 'backend_uri', 'api_uri',
                                              'remark', 'state', 'expire_in', 'refresh_expire_in'])
    manager_response.update(dict(area_coding=area_coding,
                                 area_name=get_coding_name(area_coding),
                                 street_coding=street_coding,
                                 street_name=get_coding_name(street_coding),
                                 comm_coding=comm_coding,
                                 comm_name=get_coding_name(comm_coding)))
    application = Application.objects.filter(app_id=appid).first()
    token_value = dict(appid=application.app_id, manager=manager_response)
    expire_in = manager.expire_in or application.expire_in
    cache.set('Token:{}'.format(token),
              token_value,
              expire_in)

    role = ConstRole.CityManager
    if manager_response.get('area_coding'):
        role = ConstRole.AreaManager
    if manager_response.get('street_coding'):
        role = ConstRole.StreetManager
    if manager_response.get('comm_coding'):
        role = ConstRole.CommManager
    manager_response['role'] = role
    manager_response['is_fake'] = True
    return ApiResponse(data=manager_response)


@token_required
@proxy_request()
def manager_repassword(request):
    manager = request.token_manager

    cryptor = sm2.CryptSM2(
        public_key=(
            "CDFD107FFC60C7BAB23FC377AC5DB7B1C3C64A1CC735F3AE45C8D47A79B1F222"
            "78ABD54BACF2509C4C8E826F5E2874444FAF0152540C3C99BD90C6E8AB7F0AAB"
        ),
        private_key="A271ACC51BD36EB89E1BE63CD09E8A6D1FD08C5B96394994DB8E5F4A8C56743D",
    )
    try:
        data = dict(
            password=cryptor.decrypt(bytes.fromhex(request.data.get('password'))).decode('utf-8'),
            new_password=cryptor.decrypt(bytes.fromhex(request.data.get('new_password'))).decode('utf-8')
        )
    except Exception as e:
        data = request.data


    serializer_model = ManagerRepasswordSerializer(data=data)
    if not serializer_model.is_valid():
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='提交数据有误!',
                           errors=serializer_model.errors)

    password = serializer_model.data.get('password')
    if not check_password(password=password, encoded=manager.password):
        return ApiResponse(code=ConstCode.BadRequest, msg='原密码错误.')

    manager.salt = get_random_secret(32)
    manager.set_password(serializer_model.data.get('new_password'))
    manager.save()

    ManagerJobRelation.objects.filter(manager_id=manager.id).update(sync_to_soyuan=0)
    return ApiResponse(code=ConstCode.Success)


@token_required
@proxy_request()
def manager_change_my_password(request):
    manager = request.token_manager

    serializer_model = ManagerChangeMyPasswordSerializer(data=request.data)
    if not serializer_model.is_valid():
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='提交数据有误!',
                           errors=serializer_model.errors)

    manager.salt = get_random_secret(32)
    manager.set_password(serializer_model.data.get('password'))
    manager.save()

    ManagerJobRelation.objects.filter(manager_id=manager.id).update(sync_to_soyuan=0)
    return ApiResponse(code=ConstCode.Success)


@token_required
def manager_reset_password(request):
    manager = request.token_manager

    relation_id = request.GET.get('relation_id')
    params = dict(relation_id=relation_id)
    relation = Manager.objects.filter(**params).first()
    if not relation:
        return ApiResponse(code=ConstCode.BadRequest, msg='用户不存在.')
    if relation.role != ConstRole.Organization:
        for p in ['area_coding', 'street_coding', 'comm_coding']:
            value = getattr(manager, p)
            if value:
                if getattr(relation, p) != value:
                    return ApiResponse(code=ConstCode.BadRequest, msg='用户不存在.')

    mapper = {
        "QualificationManifestDriver": ManagerResetPasswordSimpleSerializer
    }
    serializer = mapper.get(relation.role, ManagerResetPasswordSerializer)
    serializer_model = serializer(data=request.data)
    if not serializer_model.is_valid():
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='提交数据有误!',
                           errors=serializer_model.errors)

    password = serializer_model.data.get('password')
    relation.salt = get_random_secret(32)
    relation.set_password(password)
    relation.save()

    ManagerJobRelation.objects.filter(manager_id=relation.id).update(sync_to_soyuan=0)
    return ApiResponse(code=ConstCode.Success)


@token_required
def manager_reset_username(request):
    manager = request.token_manager

    relation_id = request.GET.get('relation_id')
    params = dict(relation_id=relation_id)
    relation = Manager.objects.filter(**params).first()
    if not relation:
        return ApiResponse(code=ConstCode.BadRequest, msg='用户不存在.')
    if relation.role != ConstRole.Organization:
        for p in ['area_coding', 'street_coding', 'comm_coding']:
            value = getattr(manager, p)
            if value:
                if getattr(relation, p) != value:
                    return ApiResponse(code=ConstCode.BadRequest, msg='用户不存在.')

    mapper = {
        "QualificationManifestDriver": ManagerResetUsernameSimpleSerializer
    }
    serializer = mapper.get(relation.role, ManagerResetUsernameSimpleSerializer)
    serializer_model = serializer(data=request.data)
    if not serializer_model.is_valid():
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='提交数据有误!',
                           errors=serializer_model.errors)

    username = serializer_model.data.get('username')

    if Manager.objects.filter(username=username).exists():
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='用户名(手机号)已经存在!',
                           errors=serializer_model.errors)

    relation.username = username
    relation.save()

    ManagerJobRelation.objects.filter(manager_id=relation.id).update(sync_to_soyuan=0)
    return ApiResponse(code=ConstCode.Success)


@token_required
def manager_logout(request):
    token = request.token
    if token:
        token_key = 'Token:{}'.format(token)
        manager_info = cache.get(token_key)
        if manager_info:
            manager_data = manager_info.get('manager')
            if manager_data:
                create_login_log(
                    get_ip(request),
                    manager_data['relation_id'],
                    manager_data['username'],
                    '登出成功',
                    login_type=20)

            login_id = manager_data.get('login_id')
            if login_id:
                update_logout_log(login_id)

            cache.delete(token_key)
        cache.delete('Token:{}'.format(token))
    return ApiResponse(code=ConstCode.Success)


@token_required
def manager_logoff(request):
    """注销其他管理员"""
    manager = request.token_manager
    manager_relation_id = request.GET.get('relation_id')
    manager_username = request.GET.get('username')
    if not any([manager_relation_id, manager_username]):
        return ApiResponse(code=ConstCode.BadRequest, msg='请提供管理员标识或用户名.')

    serializer_model = ManagerLogoffSerializer(data=request.data)
    if not serializer_model.is_valid():
        return ApiResponse(code=ConstCode.BadRequest,
                           msg='提交数据有误!',
                           errors=serializer_model.errors)

    query = Manager.objects
    if manager_relation_id:
        query = query.filter(relation_id=manager_relation_id)
    else:
        query = query.filter(username=manager_username)
    operator = query.first()  # type: Manager

    if not operator:
        return ApiResponse(code=ConstCode.NotExisted, msg='管理员不存在.')

    if manager.role == ConstRole.StreetManager:
        if (operator.area_coding != manager.area_coding) or (operator.street_coding != manager.street_coding):
            return ApiResponse(code=ConstCode.NotAllowed, msg='无权操作该账户.')

        if operator.role not in (ConstRole.ThrowManager,
                                 ConstRole.RfidMaker,
                                 ConstRole.CardMaker,
                                 ConstRole.PointMaker,
                                 ConstRole.Organization,
                                 ConstRole.CommManager,
                                 ConstRole.Publicists,
                                 ConstRole.DutyOfficer,
                                 ConstRole.RectifyDutyOfficer):
            return ApiResponse(code=ConstCode.NotAllowed, msg='无权操作该类账户.')

        state = 1 if serializer_model.data.get('state') else 0
        operator.state = state
        operator.save()
        return ApiResponse()
    # 再生资源交易平台
    elif (manager.role, operator.role) in [
        (ConstRole.RenewableResourcesSuper, ConstRole.RenewableResourcesTenant),
        (ConstRole.RenewableResourcesSuper, ConstRole.RenewableResourcesUser),

        (ConstRole.RenewableResourcesTenant, ConstRole.RenewableResourcesUser),

        (ConstRole.CityManager, ConstRole.RenewableCollectionUnit),
        (ConstRole.RenewableCollectionUnit, ConstRole.RenewableCollectionDriver),

        # 区管理桶站值守角色
        (ConstRole.AreaManager, ConstRole.DutyOfficer),

        # 区级审核/公司 均可管理司机用户(收运公司车辆)
        (ConstRole.Qualification, ConstRole.QualificationDriver),
        (ConstRole.Qualification, "QualificationManifestDriver"),
        (ConstRole.Qualification, role_obj.get('TransportDriver')),
        (ConstRole.AreaManager, ConstRole.QualificationDriver),
        (ConstRole.AreaManager, role_obj.get('TransportDriver')),
        # 积分商城-商户
        (ConstRole.AreaManager, role_obj.get('Merchant')),
        (ConstRole.FakeManager, role_obj.get('Merchant')),

    ]:
        state = 1 if serializer_model.data.get('state') else 0
        operator.state = state
        operator.save()
        return ApiResponse()
    # 密云镇环境智能指挥系统
    elif manager.role == role_obj.get('MiyunEnvironment'):
        state = 1 if serializer_model.data.get('state') else 0
        operator.state = state
        operator.save()
        return ApiResponse()
    elif manager.role == ConstRole.DeviceManagerAdmin and operator.role == ConstRole.DeviceCityManager:
        state = 1 if serializer_model.data.get('state') else 0
        operator.state = state
        operator.save()
        return ApiResponse()
    elif manager.role == ConstRole.AreaManager:  # 区级修改区、街道、社区账户状态
        if operator.area_coding != manager.area_coding or operator.role not in ('AreaManager', 'StreetManager', \
                                                                                'CommManager'):
            return ApiResponse(code=ConstCode.NotAllowed, msg='无权操作该账户.')
        state = 1 if serializer_model.data.get('state') else 0
        operator.state = state
        operator.save()
        return ApiResponse()

    return ApiResponse(code=ConstCode.NotAllowed, msg='无权操作.')
