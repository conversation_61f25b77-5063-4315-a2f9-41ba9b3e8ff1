#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import os
import django
from Base.utils import get_appid, get_random_secret
from django.contrib.auth.hashers import make_password

# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.import")
django.setup()

print(get_appid())
print(get_random_secret())
password = get_random_secret(12)
print(password)
print(make_password(password))
