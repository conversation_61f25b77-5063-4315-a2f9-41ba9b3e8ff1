#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import os
import binascii
import random
import string

import django
import openpyxl

from Base.utils import validate_password
from Base.utils.encrypt import password_cryptor

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.import")
django.setup()

from OAuthApp.models import Manager, ManagerJobRelation
from OAuthApp.models_base import CityRegion

role_mapping = {
    "CityManager": "市",
    "AreaManager": "区",
    "StreetManager": "街道",
    "CommManager": "社区",
    "Terminal": "末端厂",
}


def scan(roles: list):
    regions = CityRegion.objects.values("coding", "name")
    region_mapping = {r["coding"]: r["name"] for r in regions}
    list_relations = (
        ManagerJobRelation.objects
        .select_related('manager')
        .filter(manager__role__in=roles)
        .order_by("manager__area_coding", "manager__street_coding", "manager__comm_coding", "manager__role")
        .all()
    )

    # yield ["角色类型", "区", "街道", "社区", "账号", "密码", "行政编码", "是否符合规则", "新密码"]
    yield ["角色类型", "区", "街道", "社区", "账号", "密码", "行政编码", "是否禁用"]
    for relation in list_relations:
        manager = relation.manager

        # 有加密密码的进行有效性验证
        binary = binascii.a2b_hex(manager.encrypted_password)
        password = password_cryptor.decrypt(binary)
        is_valid = validate_password(password)
        shuffle_password = shuffle() if not is_valid else ""
        # if shuffle_password:
        #     relation.sync_to_soyuan = False
        #     relation.save()
        #
        #     manager.set_password(shuffle_password)
        #     manager.save()

        yield [
            role_mapping.get(manager.role, manager.role),
            region_mapping.get(manager.area_coding, ''),
            region_mapping.get(manager.street_coding, ''),
            region_mapping.get(manager.comm_coding, ''),
            manager.username,
            password,
            manager.comm_coding or manager.street_coding or manager.area_coding or '',
            # "是" if is_valid else "否",
            # shuffle_password,
            "是" if manager.state else '否'
        ]


def to_excel():
    excel = openpyxl.Workbook()

    excel.remove(excel.active)

    # 市-区-街-社
    sheet = excel.create_sheet("市区街社")
    row_no = 0
    for letter in string.ascii_uppercase[:8]:
        sheet.column_dimensions[letter].width = 20
    for row in scan(['CityManager', 'AreaManager', 'StreetManager', 'CommManager']):
        for column_no, value in enumerate(row):
            sheet.cell(row=row_no + 1, column=column_no + 1).value = value
        sheet.row_dimensions[row_no + 1].height = 15
        row_no += 1

    # # 末端
    # sheet = excel.create_sheet("末端")
    # row_no = 0
    # for letter in string.ascii_uppercase[:6]:
    #     sheet.column_dimensions[letter].width = 20
    # for row in scan(['Terminal']):
    #     row = row[:1] + row[4:]
    #     for column_no, value in enumerate(row):
    #         sheet.cell(row=row_no + 1, column=column_no + 1).value = value
    #     sheet.row_dimensions[row_no + 1].height = 15
    #     row_no += 1

    excel.save("密码明细.xlsx")


def shuffle():
    seeds = string.digits + string.ascii_letters
    password = ''.join(random.choices(seeds, k=10))
    if not validate_password(password):
        return shuffle()
    return password


if __name__ == "__main__":
    to_excel()
