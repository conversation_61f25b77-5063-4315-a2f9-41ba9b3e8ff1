# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each Foreign<PERSON>ey has `on_delete` set to the desired behavior.
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.contrib.auth.hashers import make_password
from django.db import models
from Base.utils.base import get_uuid_str
from Base.utils.encrypt import password_cryptor

STATE_CHOICES = (
    (0, u'启用'),
    (1, u'禁用'),
)


class AgencyType(models.Model):
    parent_id = models.IntegerField(default=0)
    type = models.CharField(unique=True, max_length=32)
    name = models.Char<PERSON>ield(max_length=64)
    description = models.CharField(max_length=255, blank=True)
    auth_configure_type = models.CharField(max_length=255, blank=True)
    role = models.CharField(max_length=64, blank=True)
    soyuan_role = models.CharField(max_length=64, blank=True)
    available_sites = models.Char<PERSON>ield(max_length=64, blank=True)
    sync_to_soyuan = models.IntegerField(default=0)
    state = models.IntegerField(choices=STATE_CHOICES, default=0)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        managed = False
        db_table = 'agency_type'
        app_label = 'ljfl_auth_manager_db'


class Agency(models.Model):
    parent_id = models.IntegerField()
    parent_map = models.CharField(max_length=255, blank=True)
    name = models.CharField(max_length=64)
    description = models.CharField(max_length=255, blank=True)
    auth_configure = models.CharField(max_length=255)
    is_leaf = models.IntegerField(default=1)
    is_sys = models.IntegerField(default=0)
    state = models.IntegerField(choices=STATE_CHOICES, default=0)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    agency_type = models.ForeignKey(
        'AgencyType',
        db_column='agency_type',
        max_length=32,
        on_delete=models.PROTECT,
        related_name='agency_type',
        to_field='type'
    )

    class Meta:
        managed = False
        db_table = 'agency'
        app_label = 'ljfl_auth_manager_db'


class AgencyTypeRoleSettings(models.Model):
    agency_type_id = models.IntegerField()
    navigation_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'agency_type_role_settings'
        app_label = 'ljfl_auth_manager_db'
        unique_together = (('agency_type_id', 'navigation_id'),)


class Application(models.Model):
    app_id = models.BigIntegerField(unique=True)
    app_secret = models.CharField(max_length=32)
    name = models.CharField(max_length=56)
    description = models.CharField(max_length=255)
    expire_in = models.IntegerField()
    refresh_expire_in = models.IntegerField()
    white_list = models.CharField(max_length=512)
    black_list = models.CharField(max_length=512)
    state = models.IntegerField(choices=STATE_CHOICES, default=0)
    add_time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'application'
        app_label = 'ljfl_auth_manager_db'


class CityOperationLog(models.Model):
    OPERATION_SOURCES = (
        (u'PC', u'PC端'),
        (u'MOBILE', u'手机端'),
        (u'PAD', u'PAD端'),
    )
    operation_type = models.CharField(max_length=56, default='LOGIN')
    operation_source = models.CharField(max_length=56, default='PC', choices=OPERATION_SOURCES)
    username = models.CharField(max_length=56)
    relation_id = models.CharField(max_length=56)
    ip = models.CharField(max_length=255)
    content = models.CharField(max_length=255)
    version = models.CharField(max_length=56)
    is_deleted = models.IntegerField(default=0)
    create_time = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'city_operation_log'
        app_label = 'ljfl_auth_manager_db'


class Department(models.Model):
    name = models.CharField(max_length=56)
    state = models.IntegerField(choices=STATE_CHOICES, default=0)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    agency_id = models.ForeignKey(
        'Agency',
        db_column='agency_id',
        on_delete=models.PROTECT,
        related_name='agency',
    )

    class Meta:
        managed = False
        db_table = 'department'
        app_label = 'ljfl_auth_manager_db'


class Job(models.Model):
    name = models.CharField(max_length=56)
    state = models.IntegerField(choices=STATE_CHOICES, default=0)
    sync_to_soyuan = models.IntegerField(default=1)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    agency_id = models.ForeignKey(
        Agency,
        db_column='agency_id',
        on_delete=models.PROTECT,
        related_name='agency',
    )
    department_id = models.ForeignKey(
        Department,
        db_column='department_id',
        on_delete=models.PROTECT,
        related_name='department',
    )

    class Meta:
        managed = False
        db_table = 'job'
        app_label = 'ljfl_auth_manager_db'


class Manager(models.Model):
    app_id = models.BigIntegerField()
    role = models.CharField(max_length=56)
    relation_id = models.CharField(unique=True, max_length=56, default=get_uuid_str)
    username = models.CharField(unique=True, max_length=56)
    email = models.CharField(unique=True, max_length=56, null=True, blank=True)
    password = models.CharField(max_length=128)
    encrypted_password = models.CharField(max_length=255, blank=True)
    realname = models.CharField(max_length=64, blank=True)
    phone = models.CharField(max_length=64, blank=True, null=True)
    salt = models.CharField(max_length=32)
    city_coding = models.CharField(max_length=12)
    city_name = models.CharField(max_length=56)
    area_coding = models.CharField(max_length=12, blank=True)
    area_name = models.CharField(max_length=56, blank=True)
    street_coding = models.CharField(max_length=12, blank=True)
    street_name = models.CharField(max_length=56, blank=True)
    comm_coding = models.CharField(max_length=12, blank=True)
    comm_name = models.CharField(max_length=56, blank=True)
    extra_configure = models.JSONField(null=True)
    front_uri = models.CharField(max_length=255, blank=True)
    backend_uri = models.CharField(max_length=255, blank=True)
    api_uri = models.CharField(max_length=255, blank=True)
    remark = models.CharField(max_length=255, blank=True)
    expire_in = models.IntegerField(default=0)
    refresh_expire_in = models.IntegerField(default=0)
    is_declare = models.IntegerField(default=0)
    state = models.IntegerField(choices=STATE_CHOICES, default=0)
    add_time = models.DateTimeField()
    update_time = models.DateTimeField(auto_now=True, editable=True)

    def set_password(self, password):
        password = password.strip()
        pbkdf2_sha256_password = make_password(password)
        encrypted_password = password_cryptor.encrypt(password).hex()
        self.password = pbkdf2_sha256_password
        self.encrypted_password = encrypted_password

    class Meta:
        managed = False
        db_table = 'manager'
        app_label = 'ljfl_auth_manager_db'


class Role(models.Model):
    role = models.CharField(max_length=56)
    name = models.CharField(max_length=56)
    is_deleted = models.IntegerField()
    create_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'role'
        app_label = 'ljfl_auth_manager_db'


class ManagerJobRelation(models.Model):
    agency_id = models.IntegerField()
    department_id = models.IntegerField()
    job_id = models.IntegerField()
    is_sys = models.IntegerField(default=0)
    sync_to_soyuan = models.IntegerField(default=0)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    manager = models.ForeignKey(
        Manager,
        db_column='manager_id',
        on_delete=models.PROTECT,
        related_name='manager',
    )

    class Meta:
        managed = False
        db_table = 'manager_job_relation'
        app_label = 'ljfl_auth_manager_db'
        unique_together = (('job_id', 'manager_id'),)


class JobRoleSettings(models.Model):
    job_id = models.IntegerField()
    navigation_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'job_role_settings'
        app_label = 'ljfl_auth_manager_db'
        unique_together = (('job_id', 'navigation_id'),)


class Navigation(models.Model):
    TYPE_CHOICES = (
        (u'FOLDER', u'目录'),
        (u'MENU', u'菜单'),
        (u'BUTTON', u'按钮'),
    )

    site_id = models.IntegerField()
    parent_id = models.IntegerField(default=0)
    type = models.CharField(max_length=16, choices=TYPE_CHOICES)
    name = models.CharField(max_length=64)
    permission = models.CharField(max_length=64, blank=True)
    component_name = models.CharField(max_length=64, blank=True)
    component_path = models.CharField(max_length=255, blank=True)
    icon = models.CharField(max_length=255, blank=True)
    is_frame = models.IntegerField(default=0)
    is_cache = models.IntegerField(default=0)
    is_visible = models.IntegerField(default=1)
    path = models.CharField(max_length=255, blank=True)
    active_navigation = models.CharField(max_length=255, blank=True)
    sort = models.IntegerField(default=99)
    state = models.IntegerField(choices=STATE_CHOICES, default=0)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        managed = False
        db_table = 'navigation'
        app_label = 'ljfl_auth_manager_db'
        ordering = ('sort', 'id')


class OperationLog(models.Model):
    ip = models.CharField(max_length=255)
    username = models.CharField(max_length=56)
    login_type = models.IntegerField()
    content = models.CharField(max_length=255)
    create_time = models.DateTimeField(auto_now_add=True)
    is_deleted = models.IntegerField(default=0)
    relation_id = models.CharField(max_length=56)

    class Meta:
        managed = False
        db_table = 'operation_log'
        app_label = 'ljfl_auth_manager_db'


class Site(models.Model):
    code = models.CharField(unique=True, max_length=64)
    name = models.CharField(max_length=64)
    description = models.CharField(max_length=255)
    uri = models.CharField(max_length=255)
    state = models.IntegerField(choices=STATE_CHOICES, default=0)
    sort = models.IntegerField(default=99)

    class Meta:
        managed = False
        db_table = 'site'
        app_label = 'ljfl_auth_manager_db'
        ordering = ('sort', 'id')


class CityOperationOnLog(models.Model):
    OPERATION_SOURCES = (
        (u'PC', u'PC端'),
        (u'MOBILE', u'手机端'),
        (u'PAD', u'PAD端'),
    )
    operation_type = models.CharField(max_length=56, default='LOGIN')
    operation_source = models.CharField(max_length=56, default='PC', choices=OPERATION_SOURCES)
    username = models.CharField(max_length=56)
    # relation_id = models.CharField(max_length=56)
    relation = models.ForeignKey("Manager", db_column='relation_id', on_delete=models.PROTECT, to_field="relation_id", db_constraint=False)
    ip = models.CharField(max_length=255)
    content = models.CharField(max_length=255)
    version = models.CharField(max_length=56)
    is_deleted = models.IntegerField(default=0)
    login_id = models.CharField(max_length=36)
    login_time = models.DateTimeField(auto_now_add=True)
    logout_time = models.DateTimeField(null=True)

    class Meta:
        managed = False
        db_table = 'city_operation_on_log'
        app_label = 'ljfl_auth_manager_db'
        ordering = ('-id',)
