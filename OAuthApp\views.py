# Create your views here.
import datetime
import json

from django.db import transaction
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import generics, filters, viewsets, status
from rest_framework.response import Response

from Base.api import ApiPageNumberPagination, ConstCode, ApiResponse, get_logger
from Base.plugin import ResponseError
from Base.utils import get_ip, get_random_secret
from Lic.core import SitePermission, login, token_required, GeneralPermission
from . import services
from .filters import NavigationFilter, JobStaffFilter, JobFilter
from .models import OperationLog, Manager, Navigation, Agency, AgencyType, Department, Job, Site, ManagerJobRelation, \
    AgencyTypeRoleSettings, JobRoleSettings
from .models_base import CityRegion
from .serializers import ManagerLoginLogSer, ManagerSer, NavigationSer, AgencySer, AgencyTypeSer, DepartmentSer, JobSer, \
    SiteSer, StaffSer, JobStaffSer, AgencyTypeRoleSettingsCreateSer, JobRoleSettingsCreateSer, \
    ManagerResetPasswordSerializer

logger = get_logger('django')


class TokenView(generics.RetrieveAPIView):

    def get(self, request, *args, **kwargs):
        return login(request)


class ManagerView(generics.RetrieveAPIView, generics.CreateAPIView, generics.UpdateAPIView):

    def get(self, request, *args, **kwargs):
        return services.manager_by_token(request)

    def post(self, request, *args, **kwargs):
        return services.manager_register(request)

    def patch(self, request, *args, **kwargs):
        return services.manager_fake_region(request)


class RefreshTokenView(generics.RetrieveAPIView):

    def get(self, request, *args, **kwargs):
        return services.do_refresh_token(request)


class RepasswordView(generics.CreateAPIView):

    def post(self, request, *args, **kwargs):
        return services.manager_repassword(request)


class ChangeMyPasswordView(generics.CreateAPIView):

    def post(self, request, *args, **kwargs):
        return services.manager_change_my_password(request)



class ResetPasswordView(generics.CreateAPIView):

    def post(self, request, *args, **kwargs):
        return services.manager_reset_password(request)


class ResetUsernameView(generics.CreateAPIView):

    def post(self, request, *args, **kwargs):
        return services.manager_reset_username(request)


class LogoutView(generics.CreateAPIView):

    def post(self, request, *args, **kwargs):
        return services.manager_logout(request)


class ManagerLogoffView(generics.CreateAPIView):

    def post(self, request, *args, **kwargs):
        return services.manager_logoff(request)


class ManagerOperationLogView(generics.ListCreateAPIView):
    permission_classes = (GeneralPermission,)
    queryset = OperationLog.objects.filter(is_deleted=0).order_by('-create_time')
    serializer_class = ManagerLoginLogSer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter,)
    filterset_fields = ('login_type',)
    search_fields = ('username',)
    pagination_class = ApiPageNumberPagination

    def list(self, request, *args, **kwargs):
        params = request.query_params.dict()
        manager = request.token_manager

        spec = {
            'relation_id': manager.relation_id,
        }
        # 如果没传类型 默认为登录日志
        login_type = params.get('login_type')
        if not login_type:
            spec['login_type__in'] = [10, 20]

        start_date = params.get('start_date')
        end_date = params.get('end_date')
        if start_date and end_date:
            dt = datetime.datetime.strptime(end_date, "%Y-%m-%d")
            end_date = (dt + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
            spec['create_time__gte'] = start_date
            spec['create_time__lt'] = end_date

        queryset = self.filter_queryset(self.queryset.filter(**spec))
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return ApiResponse(data=serializer.data)

    def create(self, request, *args, **kwargs):
        params = request.data.copy()
        manager = request.token_manager

        params['ip'] = get_ip(request)
        params['relation_id'] = manager.relation_id
        params['username'] = manager.username
        params['login_type'] = 30

        serializer = self.get_serializer(data=params)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return ApiResponse(data=serializer.data)


class AccountListfView(generics.ListCreateAPIView):
    permission_classes = (GeneralPermission,)
    queryset = Manager.objects.filter(state=0).order_by('-add_time')
    serializer_class = ManagerSer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter,)
    filterset_fields = ('role', 'username', 'area_coding')
    search_fields = ('username',)
    pagination_class = ApiPageNumberPagination

    def list(self, request, *args, **kwargs):
        # #验证token
        # verify_result = verify_token(request)
        # if verify_result:#过期
        #     return verify_result

        area_coding = request.GET.get('area_coding', '')
        street_coding = request.GET.get('street_coding', '')
        comm_coding = request.GET.get('comm_coding', '')
        role = request.GET.get('role', '')
        username = request.GET.get('username', '')
        queryset = self.filter_queryset(self.queryset.filter())
        if not (area_coding or street_coding or comm_coding) or not (
            role in ['AreaManager', 'StreetManager', 'CommManager']):
            queryset = queryset.none()
        else:
            queryset = queryset.filter(role=role)
            if area_coding:
                queryset = queryset.filter(area_coding=area_coding)
            if street_coding:
                queryset = queryset.filter(street_coding=street_coding)
            if comm_coding:
                queryset = queryset.filter(street_coding=street_coding)
        if username:
            queryset = queryset.filter(username=username)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return ApiResponse(data=serializer.data)


@token_required
def verify_token(request):
    pass


class SiteView(viewsets.ReadOnlyModelViewSet):
    permission_classes = (SitePermission,)
    queryset = Site.objects.order_by('id')
    serializer_class = SiteSer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter,)
    filterset_fields = ('state',)
    search_fields = ('name',)


class NavigationView(viewsets.ModelViewSet):
    permission_classes = (SitePermission,)
    queryset = Navigation.objects.order_by('sort', 'id')
    serializer_class = NavigationSer
    search_fields = ('name',)
    filter_class = NavigationFilter

    def perform_destroy(self, instance):
        # 1. 判定下级导航
        check_children = Navigation.objects.filter(parent_id=instance.id).exists()
        if check_children:
            raise ResponseError(ConstCode.BadRequest, '存在子级 目录/菜单/按钮, 不可删除.', status_code=200)

        instance.delete()


class AgencyTypeView(viewsets.ReadOnlyModelViewSet):
    permission_classes = (SitePermission,)
    queryset = AgencyType.objects.exclude(id=1).order_by('id')
    serializer_class = AgencyTypeSer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter,)
    filterset_fields = ('type', 'state', 'parent_id')
    search_fields = ('name',)


class AgencyView(viewsets.ModelViewSet):
    permission_classes = (SitePermission,)
    queryset = Agency.objects.exclude(id=1).order_by('id').select_related('agency_type')
    serializer_class = AgencySer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter,)
    filterset_fields = ('parent_id', 'agency_type', 'state')
    search_fields = ('name',)

    def get_queryset(self):
        request = self.request
        manager_relation = request.token_manager_job_relation
        agency_id = manager_relation.get('agency_id')
        parent_id = request.query_params.get('parent_id')

        queryset = self.queryset
        if not parent_id and request.path.endswith('/agency/'):
            if agency_id == 1:
                queryset = queryset.filter(parent_id=0)
            else:
                queryset = queryset.filter(id=agency_id)
        else:
            parent = Agency.objects.filter(id=parent_id).first()
            if parent:
                parent_map = parent.parent_map if parent else ''
                parent_map = f'{parent_map}{"," if parent_map else ""}{parent.id}'
                queryset = queryset.filter(parent_map__startswith=parent_map).filter(parent_id=parent_id)
        return queryset

    def perform_create(self, serializer):
        parent_map = ''
        parent_id = serializer.validated_data['parent_id']
        if parent_id:
            parent = Agency.objects.filter(id=parent_id).first()
            parent_map = parent.parent_map if parent else ''
            parent_map = f'{parent_map}{"," if parent_map else ""}{parent.id}'
        serializer.validated_data['parent_map'] = parent_map
        serializer.save()
        Agency.objects.filter(id=serializer.instance.parent_id).update(is_leaf=0)

    def perform_update(self, serializer):
        try:
            with transaction.atomic(using='ljfl_auth_manager_db'):
                instance = self.get_object()
                history_parent_id = instance.parent_id
                history_parent_map = f'{instance.parent_map}{"," if instance.parent_map else ""}{instance.id}'
                check_children = Agency.objects.filter(parent_id=instance.parent_id).exclude(id=instance.id).exists()
                if not check_children:
                    Agency.objects.filter(id=instance.parent_id).update(is_leaf=1)

                serializer.save()

                instance = serializer.instance
                if instance.parent_id == history_parent_id:
                    Agency.objects.filter(id=instance.parent_id).update(is_leaf=0)
                else:
                    # 更新当前机构map
                    parent = Agency.objects.filter(id=instance.parent_id).first()
                    parent_map = parent.parent_map if parent else ''
                    parent_map = f'{parent_map}{"," if parent_map else ""}{parent.id}'
                    instance.parent_map = parent_map
                    instance.save()
                    new_parent_map = f'{parent_map},{instance.id}'

                    # 将当前机构子项map进行更新
                    children = Agency.objects.filter(parent_map__startswith=history_parent_map).all()
                    for agency in children:
                        agency.parent_map = agency.parent_map.replace(history_parent_map, new_parent_map, 1)
                        agency.save()
        except Exception as e:
            if isinstance(e, ResponseError):
                raise e
            logger.error('Error occur on create stuff.')
            logger.exception(e)
            raise ResponseError(ConstCode.BadRequest, '添加失败, 请稍候重试.', status_code=200)

    def perform_destroy(self, instance):
        # 1. 判定下级机构
        check_children = Agency.objects.filter(parent_id=instance.id).exists()
        if check_children:
            raise ResponseError(ConstCode.BadRequest, '存在下属机构, 不可删除.', status_code=200)

        # 2. 判定部门
        check_department = Department.objects.filter(agency_id=instance.id).exists()
        if check_department:
            raise ResponseError(ConstCode.BadRequest, '存在下属部门, 不可删除.', status_code=200)

        # 3. 判定管理员
        check_manager = ManagerJobRelation.objects.filter(agency_id=instance.id).exists()
        if check_manager:
            raise ResponseError(ConstCode.BadRequest, '存在管理用户, 不可删除.', status_code=200)

        try:
            with transaction.atomic(using='ljfl_auth_manager_db'):
                parent = Agency.objects.filter(id=instance.parent_id).first()
                if parent:
                    check_siblings = Agency.objects.filter(parent_id=parent.id).exclude(id=instance.id).exists()
                    if not check_siblings:
                        parent.is_leaf = 1
                        parent.save()
                instance.delete()
        except Exception as e:
            if isinstance(e, ResponseError):
                raise e
            logger.error('Error occur on delete stuff.')
            logger.exception(e)
            raise ResponseError(ConstCode.BadRequest, '删除失败, 请稍候重试.', status_code=200)


class DepartmentView(viewsets.ModelViewSet):
    permission_classes = (SitePermission,)
    queryset = Department.objects.order_by('id')
    serializer_class = DepartmentSer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    filterset_fields = ('agency_id', 'state')
    search_fields = ('name',)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        params = request.query_params.dict()
        data = []
        if params.get('with_sys'):
            now = datetime.datetime.now()
            data.append(
                {
                    "id": 0,
                    "agency_id": 0,
                    "state_name": "启用",
                    "name": "系统",
                    "state": 0,
                    "create_time": now,
                    "update_time": now
                }
            )
        data.extend(serializer.data)
        return Response(data)

    def perform_destroy(self, instance):
        # 1. 判定职务
        check_job = Job.objects.filter(department_id=instance.id).exists()
        if check_job:
            raise ResponseError(ConstCode.BadRequest, '存在下属职务, 不可删除.', status_code=200)

        # 2. 判定管理员
        check_manager = ManagerJobRelation.objects.filter(department_id=instance.id).exists()
        if check_manager:
            raise ResponseError(ConstCode.BadRequest, '存在管理用户, 不可删除.', status_code=200)

        instance.delete()


class JobView(viewsets.ModelViewSet):
    permission_classes = (SitePermission,)
    queryset = Job.objects.order_by('id')
    serializer_class = JobSer
    filterset_class = JobFilter
    search_fields = ('name',)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        params = request.query_params.dict()
        data = []
        if params.get('with_sys') and params.get("department_id") == "0":
            now = datetime.datetime.now()
            data.append(
                {
                    "id": 0,
                    "agency_id": 0,
                    "department_id": 0,
                    "state_name": "启用",
                    "name": "管理员",
                    "state": 0,
                    "create_time": now,
                    "update_time": now
                }
            )
        data.extend(serializer.data)
        return Response(data)

    def perform_destroy(self, instance):
        # 1. 判定管理员
        check_manager = ManagerJobRelation.objects.filter(job_id=instance.id).exists()
        if check_manager:
            raise ResponseError(ConstCode.BadRequest, '存在管理用户, 不可删除.', status_code=200)

        instance.delete()


class StaffView(viewsets.ModelViewSet):
    permission_classes = (SitePermission,)
    queryset = Manager.objects.order_by('-update_time', '-add_time').all()
    serializer_class = StaffSer
    filter_backends = (DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter,)
    filterset_fields = ('state',)
    search_fields = ('id', 'username', 'realname')
    pagination_class = ApiPageNumberPagination

    def get_queryset(self):
        staff = ManagerJobRelation.objects

        request = self.request
        params = request.query_params.dict()
        param_scope = params.get('scope', 'clan')
        manager_relation = request.token_manager_job_relation
        agency_id = manager_relation.get('agency_id')
        agency = Agency.objects.filter(id=agency_id).first()
        if agency and agency.agency_type.type != 'Platform':
            parent_map = f'{agency.parent_map}{"," if agency.parent_map else ""}{agency.id}'
            agency_ids = Agency.objects
            if param_scope == 'siblings':
                agency_ids = agency_ids.filter(id=agency_id)
            else:
                agency_ids = agency_ids.filter(Q(id=agency_id) | Q(parent_map__startswith=parent_map))
            agency_ids = agency_ids.values_list('id')
            staff = staff.filter(agency_id__in=agency_ids)

        # # 平台管理员不可管理
        # if agency and agency.agency_type.type == 'Platform':
        #     pass
        # else:
        #     platform_agency_ids = Agency.objects.filter(agency_type="Platform").values_list("id", flat=True)
        #     staff = staff.exclude(agency_id__in=platform_agency_ids)
        platform_agency_ids = Agency.objects.filter(agency_type="Platform").values_list("id", flat=True)
        staff = staff.exclude(agency_id__in=platform_agency_ids)

        staff = staff.values_list('manager_id')
        queryset = self.queryset.filter(id__in=staff)
        return queryset

    def perform_create(self, serializer):
        job_ids = serializer.validated_data.pop('job_ids', [])
        is_sys = serializer.validated_data.pop('is_sys', 0)
        agency_id = serializer.validated_data.pop('agency_id')
        password = serializer.validated_data.pop('password')
        data = serializer.validated_data

        if not is_sys and not job_ids:
            raise ResponseError(ConstCode.BadRequest, '请至少保留一个职务.', status_code=200)

        username = data.get('username').replace(" ", "")
        check_username = Manager.objects.filter(username=username).exists()
        if check_username:
            raise ResponseError(ConstCode.BadRequest, '用户名已存在.', status_code=200)

        agency = Agency.objects.filter(id=agency_id, state=0).first()
        if not agency:
            raise ResponseError(ConstCode.BadRequest, '机构不存在.', status_code=200)

        try:
            with transaction.atomic(using='ljfl_auth_manager_db'):

                manager = Manager(
                    app_id=117998218,
                    role=agency.agency_type.role,
                    username=username,
                    realname=data.get('realname'),
                    phone=data.get('phone', ""),
                    salt=get_random_secret(),
                    city_coding='110000000000',
                    city_name='北京市',
                    state=data.get('state'),
                    add_time=datetime.datetime.now()
                )

                manager.set_password(password)

                auth_configure = json.loads(agency.auth_configure)
                auth_type = auth_configure.get('type')
                if auth_type == 'REGION':
                    for key, val in auth_configure.get('value').items():
                        if key in ['area_coding', 'street_coding', 'comm_coding']:
                            setattr(manager, key, val)
                            region = CityRegion.objects.filter(is_deleted=0, coding=val).first()
                            if region:
                                setattr(manager, key.replace('coding', 'name'), region.name)

                manager.save()

                # 1. 如果是系统管理员，不设置管理员 职务 关联关系
                if is_sys == 1:
                    relation = ManagerJobRelation(
                        agency_id=agency.id,
                        department_id=0,
                        job_id=0,
                        manager_id=manager.id,
                        is_sys=1
                    )
                    relation.save()
                else:
                    relations = []
                    for jid in job_ids:
                        job = Job.objects.filter(id=jid, state=0).first()
                        if not job:
                            raise ResponseError(ConstCode.BadRequest, '选择的职务不存在.', status_code=200)

                        if job.agency_id.id != agency.id:
                            raise ResponseError(ConstCode.BadRequest, '不允许跨机构选择职务.', status_code=200)

                        relations.append(ManagerJobRelation(
                            agency_id=agency.id,
                            department_id=job.department_id.id,
                            job_id=job.id,
                            manager_id=manager.id,
                            is_sys=0
                        ))

                    for rel in relations:
                        rel.save()

        except Exception as e:
            if isinstance(e, ResponseError):
                raise e

            logger.error('Error occur on create stuff.')
            logger.exception(e)

            raise ResponseError(ConstCode.BadRequest, '添加失败, 请稍候重试.', status_code=200)

    def perform_update(self, serializer):
        job_ids = serializer.validated_data.pop('job_ids', [])
        is_sys = serializer.validated_data.pop('is_sys', 0)
        agency_id = serializer.validated_data.pop('agency_id')
        password = serializer.validated_data.pop('password')
        data = serializer.validated_data

        if not is_sys and not job_ids:
            raise ResponseError(ConstCode.BadRequest, '请至少保留一个职务.', status_code=200)

        try:
            with transaction.atomic(using='ljfl_auth_manager_db'):
                manager = serializer.instance
                agency = Agency.objects.filter(id=agency_id, state=0).first()

                manager.role = agency.agency_type.role
                if password != '0|0|0|0|0|0' and password != 'oO0000Oo':
                    manager.set_password(password)
                manager.realname = data.get('realname')
                manager.phone = data.get('phone')
                manager.salt = get_random_secret()
                manager.state = data.get('state')

                auth_configure = json.loads(agency.auth_configure)
                auth_type = auth_configure.get('type')
                if auth_type == 'REGION':
                    for key, val in auth_configure.get('value').items():
                        if key in ['area_coding', 'street_coding', 'comm_coding']:
                            setattr(manager, key, val)
                            region = CityRegion.objects.filter(is_deleted=0, coding=val).first()
                            if region:
                                setattr(manager, key.replace('coding', 'name'), region.name)

                manager.save()

                ManagerJobRelation.objects.filter(manager_id=manager.id).delete()

                # 1. 如果是系统管理员，不设置管理员 职务 关联关系
                if is_sys == 1:
                    relation = ManagerJobRelation(
                        agency_id=agency.id,
                        department_id=0,
                        job_id=0,
                        manager_id=manager.id,
                        is_sys=1
                    )
                    relation.save()
                else:
                    relations = []
                    for jid in job_ids:
                        job = Job.objects.filter(id=jid, state=0).first()
                        if not job:
                            raise ResponseError(ConstCode.BadRequest, '选择的职务不存在.', status_code=200)

                        if job.agency_id.id != agency.id:
                            raise ResponseError(ConstCode.BadRequest, '不允许跨机构选择职务.', status_code=200)

                        relations.append(ManagerJobRelation(
                            agency_id=agency.id,
                            department_id=job.department_id.id,
                            job_id=job.id,
                            manager_id=manager.id,
                            is_sys=0
                        ))

                    for rel in relations:
                        rel.save()

        except Exception as e:
            if isinstance(e, ResponseError):
                raise e

            logger.error('Error occur on update stuff.')
            logger.exception(e)

            raise ResponseError(ConstCode.BadRequest, '修改失败, 请稍候重试.', status_code=200)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = ManagerResetPasswordSerializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        password = serializer.validated_data.get('password')

        try:
            with transaction.atomic(using='ljfl_auth_manager_db'):
                manager = serializer.instance
                manager.set_password(password)
                manager.save()

                ManagerJobRelation.objects.filter(manager_id=manager.id).update(sync_to_soyuan=0)
        except Exception as e:
            if isinstance(e, ResponseError):
                raise e

            logger.error('Error occur on partial update stuff.')
            logger.exception(e)

            raise ResponseError(ConstCode.BadRequest, '修改密码失败, 请稍候重试.', status_code=200)

        return Response()

    def perform_destroy(self, instance):
        instance.state = 0 if instance.state else 1
        ManagerJobRelation.objects.filter(manager_id=instance.id).update(sync_to_soyuan=0)
        instance.save()


class JobStaffView(generics.ListAPIView, generics.CreateAPIView, generics.DestroyAPIView):
    permission_classes = (SitePermission,)
    queryset = ManagerJobRelation.objects.select_related('manager').all()
    serializer_class = StaffSer
    filterset_fields = ('agency_id', 'department_id', 'job_id')
    filter_class = JobStaffFilter

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        manager_id_queryset = queryset.values_list('manager_id')

        queryset = Manager.objects.filter(id__in=manager_id_queryset).all()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        serializer = JobStaffSer(data=dict(
            job_id=request.GET.get('job_id'),
            manager_ids=request.data
        ))
        serializer.is_valid(raise_exception=True)

        data = serializer.validated_data

        job = Job.objects.filter(id=data.get('job_id')).first()
        if not job:
            raise ResponseError(ConstCode.BadRequest, '职务不存在.', status_code=200)

        try:
            with transaction.atomic(using='ljfl_auth_manager_db'):
                for mid in data.get('manager_ids', []):
                    manager = Manager.objects.filter(id=mid).first()
                    if not manager:
                        raise ResponseError(ConstCode.BadRequest, '管理员不存在.', status_code=200)

                    # 1. 本机构系统管理员不可担任职务
                    check_is_sys = ManagerJobRelation.objects.filter(
                        manager_id=manager.id,
                        is_sys=1
                    ).exists()
                    if check_is_sys:
                        raise ResponseError(ConstCode.BadRequest, '系统管理员不可设置职务.', status_code=200)

                    # 2. 判断是否在本部门
                    check_this_depart = ManagerJobRelation.objects.filter(
                        manager_id=manager.id, department_id=job.department_id.id
                    ).exists()
                    if not check_this_depart:
                        raise ResponseError(ConstCode.BadRequest,
                                            f'管理员 {manager.realname} ({manager.username})不在本部门.',
                                            status_code=200)

                    # 3. 管理员不可在其他部门任职
                    check_other_depart = ManagerJobRelation.objects.filter(
                        manager_id=manager.id
                    ).exclude(
                        department_id=job.department_id.id
                    ).exists()
                    if check_other_depart:
                        raise ResponseError(ConstCode.BadRequest, '管理员不可跨部门管理.', status_code=200)

                    # 4. 修改
                    ManagerJobRelation.objects.update_or_create(
                        job_id=job.id,
                        manager_id=manager.id,
                        defaults=dict(
                            agency_id=job.agency_id.id,
                            department_id=job.department_id.id,
                            is_sys=0,
                            sync_to_soyuan=0
                        )
                    )

        except Exception as e:
            if isinstance(e, ResponseError):
                raise e

            logger.error('Error occur on create manager job relation.')
            logger.exception(e)

            raise ResponseError(ConstCode.BadRequest, '添加失败, 请稍候重试.', status_code=200)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def delete(self, request, *args, **kwargs):
        job_id = request.GET.get('job_id')
        manager_id = request.GET.get('manager_id')
        instance = ManagerJobRelation.objects.filter(manager_id=manager_id, job_id=job_id).first()
        if not instance:
            raise ResponseError(ConstCode.BadRequest, '管理员不存在.', status_code=200)

        manager = Manager.objects.filter(id=instance.manager_id).first()
        if not manager:
            raise ResponseError(ConstCode.BadRequest, '管理员不存在.', status_code=200)

        if instance.is_sys == 1:
            raise ResponseError(ConstCode.BadRequest, '系统管理员不可移除.', status_code=200)

        check_job = ManagerJobRelation.objects.filter(manager_id=instance.manager_id).exclude(id=instance.id).exists()
        if not check_job:
            raise ResponseError(ConstCode.BadRequest, '管理员至少需要保持一个职务.', status_code=200)

        instance.delete()
        return Response()


class AgencyTypeRoleSettingsView(generics.ListAPIView, generics.UpdateAPIView):
    permission_classes = (SitePermission,)
    queryset = AgencyType.objects.filter(state=0).all()

    def list(self, request, *args, **kwargs):
        agency_type = AgencyType.objects.filter(id=kwargs.get('agency_type_id')).first()
        if not agency_type:
            return ApiResponse(ConstCode.NotFound, msg='机构类型不存在.')

        site_codes = [st for st in agency_type.available_sites.split(',') if st]
        sites = Site.objects.filter(code__in=site_codes).all()
        if not sites:
            return ApiResponse(ConstCode.NotFound, msg='该机构类型无站点权限.')

        results = list()
        for st in sites:
            ser_site = SiteSer(st).data
            _navigations = Navigation.objects.filter(site_id=st.id).order_by('sort', 'id').all()
            ser_navigations = NavigationSer(_navigations, many=True)
            _navi_ids = AgencyTypeRoleSettings.objects.filter(agency_type_id=agency_type.id) \
                .values_list('navigation_id', flat=True)
            for navi in ser_navigations.data:
                navi.update(dict(is_selected=1 if navi.get('id') in _navi_ids else 0))
            ser_site['navigations'] = ser_navigations.data
            results.append(ser_site)
        return Response(results)

    def update(self, request, *args, **kwargs):
        agency_type = AgencyType.objects.filter(id=kwargs.get('agency_type_id')).first()
        if not agency_type:
            return ApiResponse(ConstCode.NotFound, msg='机构类型不存在.')

        serializer = AgencyTypeRoleSettingsCreateSer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.validated_data.get('navigation_ids')
        settings = list()
        navigation_ids = Navigation.objects.filter(id__in=data).values_list('id', flat=True)
        for nav_id in navigation_ids:
            settings.append(AgencyTypeRoleSettings(agency_type_id=agency_type.id, navigation_id=nav_id))

        try:
            with transaction.atomic(using='ljfl_auth_manager_db'):
                agency_type.sync_to_soyuan = 0
                agency_type.save()
                AgencyTypeRoleSettings.objects.filter(agency_type_id=agency_type.id).delete()
                AgencyTypeRoleSettings.objects.bulk_create(settings)

        except Exception as e:
            if isinstance(e, ResponseError):
                raise e

            logger.error('Error occur on create agency type settings.')
            logger.exception(e)

            raise ResponseError(ConstCode.BadRequest, '权限设置失败, 请稍候重试.', status_code=200)

        return Response()


class JobRoleSettingsView(generics.ListAPIView, generics.UpdateAPIView):
    permission_classes = (SitePermission,)
    queryset = Job.objects.filter(state=0).all()

    def list(self, request, *args, **kwargs):
        job = Job.objects.filter(id=kwargs.get('job_id')).first()
        if not job:
            return ApiResponse(ConstCode.NotFound, msg='职务不存在.')

        agency = job.agency_id
        agency_type = agency.agency_type

        site_codes = [st for st in agency_type.available_sites.split(',') if st]
        sites = Site.objects.filter(code__in=site_codes).all()
        if not sites:
            return ApiResponse(ConstCode.NotFound, msg='该机构类型无站点权限.')

        results = list()
        for st in sites:
            ser_site = SiteSer(st).data
            _at_ids = AgencyTypeRoleSettings.objects.filter(agency_type_id=agency_type.id).values_list('navigation_id')
            _navigations = Navigation.objects.filter(site_id=st.id, id__in=_at_ids).order_by('sort', 'id').all()
            ser_navigations = NavigationSer(_navigations, many=True)
            _navi_ids = JobRoleSettings.objects.filter(job_id=job.id) \
                .values_list('navigation_id', flat=True)
            for navi in ser_navigations.data:
                navi.update(dict(is_selected=(1 if navi.get('id') in _navi_ids else 0)))
            ser_site['navigations'] = ser_navigations.data
            results.append(ser_site)
        return Response(results)

    def update(self, request, *args, **kwargs):
        job = Job.objects.filter(id=kwargs.get('job_id')).first()
        if not job:
            return ApiResponse(ConstCode.NotFound, msg='职务不存在.')

        serializer = JobRoleSettingsCreateSer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.validated_data.get('navigation_ids')
        settings = list()
        navigation_ids = Navigation.objects.filter(id__in=data).values_list('id', flat=True)
        for nav_id in navigation_ids:
            settings.append(JobRoleSettings(job_id=job.id, navigation_id=nav_id))

        try:
            with transaction.atomic(using='ljfl_auth_manager_db'):
                job.sync_to_soyuan = 0
                job.save()
                JobRoleSettings.objects.filter(job_id=job.id).delete()
                JobRoleSettings.objects.bulk_create(settings)

        except Exception as e:
            if isinstance(e, ResponseError):
                raise e

            logger.error('Error occur on create agency type settings.')
            logger.exception(e)

            raise ResponseError(ConstCode.BadRequest, '权限设置失败, 请稍候重试.', status_code=200)

        return Response()


class ManagerNavigationView(generics.RetrieveAPIView):
    permission_classes = (SitePermission,)

    def get(self, request, *args, **kwargs):
        manager_relation = request.token_manager_job_relation
        agency_id = manager_relation.get('agency_id')
        is_sys = manager_relation.get('is_sys')
        manager = request.token_manager

        relations = ManagerJobRelation.objects.filter(manager_id=manager.id).all()
        job_ids = [r.job_id for r in relations]

        site_code = request.GET.get('site_code')
        site = Site.objects.filter(code=site_code, state=0).first()
        if not site:
            return ApiResponse(code=ConstCode.Success, data=dict(navigations=[], permissions=[]))

        agency = Agency.objects.filter(id=agency_id).first()
        if agency.agency_type.type == 'Platform':
            menus = Navigation.objects.filter(site_id=site.id, type__in=['FOLDER', 'MENU']).all()
            permissions = Navigation.objects.filter(site_id=site.id, type='BUTTON').values_list('permission', flat=True)
        else:
            # 1. 获取机构类型配置
            agency_type_settings = AgencyTypeRoleSettings.objects \
                .filter(agency_type_id=agency.agency_type.id).values_list('navigation_id', flat=True)
            agency_type_nav_ids = Navigation.objects \
                .filter(site_id=site.id, id__in=agency_type_settings, state=0).values_list('id', flat=True)
            # 2. 获取职务配置
            job_nav_ids = []
            if not is_sys:
                job_settings = JobRoleSettings.objects \
                    .filter(job_id__in=job_ids).values_list('navigation_id', flat=True)
                job_nav_ids = Navigation.objects \
                    .filter(site_id=site.id, id__in=job_settings, state=0).values_list('id', flat=True)

            # 3. 获取导航菜单交集
            if is_sys:
                mix_ids = agency_type_nav_ids
            else:
                mix_ids = set(agency_type_nav_ids) & set(job_nav_ids)

            menus = Navigation.objects.filter(id__in=mix_ids, type__in=['FOLDER', 'MENU']).all()
            permissions = Navigation.objects.filter(id__in=mix_ids, type='BUTTON').values_list('permission', flat=True)

        return ApiResponse(code=ConstCode.Success,
                           data=dict(navigations=NavigationSer(menus, many=True).data,
                                     permissions=permissions))


class CityOperationOnLogDepartmentView(generics.ListAPIView):
    def list(self, request, *args, **kwargs):
        queryset = Department.objects.filter(state=0, agency_id=10).all()
        return ApiResponse(code=ConstCode.Success,
                           data=DepartmentSer(queryset, many=True).data)
